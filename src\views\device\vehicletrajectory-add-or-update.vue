<template>
  <el-dialog v-model="visible" :title="!dataForm.vehicleId ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
            <el-form-item label="${column.comments}" prop="recordTime">
        <el-input v-model="dataForm.recordTime" placeholder="${column.comments}"></el-input>
      </el-form-item>
          <el-form-item label="${column.comments}" prop="location">
        <el-input v-model="dataForm.location" placeholder="${column.comments}"></el-input>
      </el-form-item>
          <el-form-item label="${column.comments}" prop="speed">
        <el-input v-model="dataForm.speed" placeholder="${column.comments}"></el-input>
      </el-form-item>
          <el-form-item label="${column.comments}" prop="batteryLevel">
        <el-input v-model="dataForm.batteryLevel" placeholder="${column.comments}"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  vehicleId: '',  recordTime: '',  location: '',  speed: '',  batteryLevel: ''});

const rules = ref({
            recordTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          location: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          speed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          batteryLevel: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (vehicleId?: number) => {
  visible.value = true;
  dataForm.vehicleId = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (vehicleId) {
    getInfo(vehicleId);
  }
};

// 获取信息
const getInfo = (vehicleId: number) => {
  baseService.get("/device/vehicletrajectory/" + vehicleId).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.vehicleId ? baseService.post : baseService.put)("/device/vehicletrajectory", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
