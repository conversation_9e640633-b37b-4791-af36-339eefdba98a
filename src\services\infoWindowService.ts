/**
 * 信息窗口服务，用于处理高德地图信息窗口的内容生成
 */

// 定义车辆数据接口
interface VehicleData {
  id: string;
  status: 'online' | 'charging' | 'offline';
  battery: number;
  speed: number;
  location: string;
  [key: string]: any;
}

// 创建信息窗口内容
export const createVehicleInfoContent = (vehicle: VehicleData): HTMLElement => {
  // 创建信息窗口根元素
  const contentDiv = document.createElement('div');
  contentDiv.className = 'vehicle-info-window';
  contentDiv.style.width = '250px';
  contentDiv.style.background = 'linear-gradient(to bottom, #1a3a5f, #001529)';
  contentDiv.style.borderRadius = '8px';
  contentDiv.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.5)';
  contentDiv.style.padding = '8px';
  contentDiv.style.color = 'white';
  contentDiv.style.fontFamily = 'Microsoft YaHei, sans-serif';
  contentDiv.style.border = '1px solid rgba(24, 144, 255, 0.3)';
  contentDiv.style.animation = 'fadeIn 0.3s ease-out forwards';
  
  // 添加标题栏
  contentDiv.appendChild(createTitleBar(vehicle));
  
  // 添加装饰线
  contentDiv.appendChild(createDecorationLine());
  
  // 添加电量信息
  contentDiv.appendChild(createBatteryInfo(vehicle.battery));
  
  // 添加速度信息
  contentDiv.appendChild(createInfoRow('速度', `${vehicle.speed} km/h`));
  
  // 添加位置信息
  contentDiv.appendChild(createInfoRow('位置', vehicle.location));
  
  // 添加按钮区域
  contentDiv.appendChild(createButtonArea(vehicle.id));
  
  // 添加CSS动画
  addAnimationStyles();
  
  return contentDiv;
};

// 创建标题栏
const createTitleBar = (vehicle: VehicleData): HTMLElement => {
  const titleDiv = document.createElement('div');
  titleDiv.style.display = 'flex';
  titleDiv.style.alignItems = 'center';
  titleDiv.style.marginBottom = '12px';
  
  const iconSpan = document.createElement('span');
  iconSpan.className = 'title-icon';
  iconSpan.innerHTML = '🚗';
  iconSpan.style.marginRight = '8px';
  iconSpan.style.fontSize = '18px';
  
  const titleSpan = document.createElement('span');
  titleSpan.textContent = `车辆 ${vehicle.id}`;
  titleSpan.style.fontWeight = 'bold';
  titleSpan.style.fontSize = '16px';
  titleSpan.style.flex = '1';
  
  const statusSpan = document.createElement('span');
  statusSpan.style.padding = '2px 6px';
  statusSpan.style.borderRadius = '4px';
  statusSpan.style.fontSize = '12px';
  
  if (vehicle.status === 'online') {
    statusSpan.textContent = '在线';
    statusSpan.style.backgroundColor = 'rgba(82, 196, 26, 0.2)';
    statusSpan.style.color = '#52c41a';
    statusSpan.style.border = '1px solid rgba(82, 196, 26, 0.3)';
  } else if (vehicle.status === 'charging') {
    statusSpan.textContent = '充电中';
    statusSpan.style.backgroundColor = 'rgba(250, 173, 20, 0.2)';
    statusSpan.style.color = '#faad14';
    statusSpan.style.border = '1px solid rgba(250, 173, 20, 0.3)';
  } else {
    statusSpan.textContent = '离线';
    statusSpan.style.backgroundColor = 'rgba(245, 34, 45, 0.2)';
    statusSpan.style.color = '#f5222d';
    statusSpan.style.border = '1px solid rgba(245, 34, 45, 0.3)';
  }
  
  titleDiv.appendChild(iconSpan);
  titleDiv.appendChild(titleSpan);
  titleDiv.appendChild(statusSpan);
  
  return titleDiv;
};

// 创建装饰线
const createDecorationLine = (): HTMLElement => {
  const decorationDiv = document.createElement('div');
  decorationDiv.style.height = '2px';
  decorationDiv.style.width = '100%';
  decorationDiv.style.background = 'linear-gradient(to right, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.8), rgba(24, 144, 255, 0.1))';
  decorationDiv.style.marginBottom = '12px';
  
  return decorationDiv;
};

// 创建电量信息
const createBatteryInfo = (battery: number): HTMLElement => {
  const batteryDiv = document.createElement('div');
  batteryDiv.style.marginBottom = '10px';
  batteryDiv.style.fontSize = '14px';
  batteryDiv.style.display = 'flex';
  batteryDiv.style.alignItems = 'center';
  
  const batteryLabel = document.createElement('span');
  batteryLabel.textContent = '电量: ';
  batteryLabel.style.marginRight = '8px';
  
  const progressContainer = document.createElement('div');
  progressContainer.style.flex = '1';
  progressContainer.style.height = '12px';
  progressContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.25)';
  progressContainer.style.borderRadius = '6px';
  progressContainer.style.overflow = 'hidden';
  progressContainer.style.border = '1px solid rgba(24, 144, 255, 0.3)';
  
  const progressBar = document.createElement('div');
  progressBar.style.height = '100%';
  progressBar.style.width = `${battery}%`;
  
  // 根据电量设置颜色
  if (battery <= 20) {
    progressBar.style.background = 'linear-gradient(to right, #f5222d, #ff7875)';
    progressBar.style.animation = 'pulse 1.5s infinite';
  } else if (battery <= 50) {
    progressBar.style.background = 'linear-gradient(to right, #faad14, #ffc53d)';
  } else {
    progressBar.style.background = 'linear-gradient(to right, #52c41a, #95de64)';
  }
  
  const batteryText = document.createElement('span');
  batteryText.textContent = `${battery}%`;
  batteryText.style.marginLeft = '8px';
  batteryText.style.color = battery <= 20 ? '#ff7875' : 'white';
  
  progressContainer.appendChild(progressBar);
  batteryDiv.appendChild(batteryLabel);
  batteryDiv.appendChild(progressContainer);
  batteryDiv.appendChild(batteryText);
  
  return batteryDiv;
};

// 创建信息行
const createInfoRow = (label: string, value: string): HTMLElement => {
  const infoDiv = document.createElement('div');
  infoDiv.style.marginBottom = '10px';
  infoDiv.style.fontSize = '14px';
  
  infoDiv.textContent = `${label}: ${value}`;
  
  return infoDiv;
};

// 创建按钮区域
const createButtonArea = (vehicleId: string): HTMLElement => {
  const buttonDiv = document.createElement('div');
  buttonDiv.style.display = 'flex';
  buttonDiv.style.justifyContent = 'space-between';
  buttonDiv.style.marginTop = '12px';
  
  const detailButton = document.createElement('div');
  detailButton.textContent = '查看详情';
  detailButton.style.padding = '6px 12px';
  detailButton.style.backgroundColor = 'rgba(24, 144, 255, 0.8)';
  detailButton.style.color = 'white';
  detailButton.style.borderRadius = '4px';
  detailButton.style.cursor = 'pointer';
  detailButton.style.textAlign = 'center';
  detailButton.style.flex = '1';
  detailButton.style.marginRight = '8px';
  detailButton.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
  detailButton.style.transition = 'all 0.3s ease';
  
  const trackButton = document.createElement('div');
  trackButton.textContent = '轨迹回放';
  trackButton.style.padding = '6px 12px';
  trackButton.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
  trackButton.style.color = 'white';
  trackButton.style.borderRadius = '4px';
  trackButton.style.cursor = 'pointer';
  trackButton.style.textAlign = 'center';
  trackButton.style.flex = '1';
  trackButton.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';
  trackButton.style.transition = 'all 0.3s ease';
  
  // 按钮悬停效果
  detailButton.addEventListener('mouseover', function() {
    this.style.backgroundColor = 'rgba(24, 144, 255, 1)';
    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
  });
  
  detailButton.addEventListener('mouseout', function() {
    this.style.backgroundColor = 'rgba(24, 144, 255, 0.8)';
    this.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
  });
  
  trackButton.addEventListener('mouseover', function() {
    this.style.backgroundColor = 'rgba(0, 0, 0, 0.4)';
    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.4)';
  });
  
  trackButton.addEventListener('mouseout', function() {
    this.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
    this.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';
  });
  
  // 按钮点击功能
  detailButton.addEventListener('click', function() {
    alert(`查看车辆 ${vehicleId} 的详细信息`);
  });
  
  trackButton.addEventListener('click', function() {
    alert(`查看车辆 ${vehicleId} 的轨迹回放`);
  });
  
  buttonDiv.appendChild(detailButton);
  buttonDiv.appendChild(trackButton);
  
  return buttonDiv;
};

// 添加CSS动画
const addAnimationStyles = (): void => {
  // 检查是否已经添加
  if (document.getElementById('info-window-animations')) {
    return;
  }
  
  const style = document.createElement('style');
  style.id = 'info-window-animations';
  style.textContent = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes pulse {
      0% { opacity: 0.6; }
      50% { opacity: 1; }
      100% { opacity: 0.6; }
    }
  `;
  document.head.appendChild(style);
};

export default {
  createVehicleInfoContent
}; 