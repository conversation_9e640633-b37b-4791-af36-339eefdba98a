<template>
  <el-dialog v-model="visible" :title="!dataForm.vehicleId ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
            <el-form-item label="${column.comments}" prop="status">
        <el-input v-model="dataForm.status" placeholder="${column.comments}"></el-input>
      </el-form-item>
          <el-form-item label="${column.comments}" prop="batteryLevel">
        <el-input v-model="dataForm.batteryLevel" placeholder="${column.comments}"></el-input>
      </el-form-item>
          <el-form-item label="${column.comments}" prop="currentSpeed">
        <el-input v-model="dataForm.currentSpeed" placeholder="${column.comments}"></el-input>
      </el-form-item>
          <el-form-item label="${column.comments}" prop="lastHeartbeat">
        <el-input v-model="dataForm.lastHeartbeat" placeholder="${column.comments}"></el-input>
      </el-form-item>
          <el-form-item label="${column.comments}" prop="currentLocation">
        <el-input v-model="dataForm.currentLocation" placeholder="${column.comments}"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  vehicleId: '',  status: '',  batteryLevel: '',  currentSpeed: '',  lastHeartbeat: '',  currentLocation: ''});

const rules = ref({
            status: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          batteryLevel: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          currentSpeed: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          lastHeartbeat: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          currentLocation: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (vehicleId?: number) => {
  visible.value = true;
  dataForm.vehicleId = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (vehicleId) {
    getInfo(vehicleId);
  }
};

// 获取信息
const getInfo = (vehicleId: number) => {
  baseService.get("/device/vehiclerealtimestatus/" + vehicleId).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.vehicleId ? baseService.post : baseService.put)("/device/vehiclerealtimestatus", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
