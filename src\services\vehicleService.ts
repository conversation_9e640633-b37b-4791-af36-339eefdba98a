// 使用原生WebSocket

// 车辆实时状态接口
export interface VehicleRealtimeStatus {
  deviceName: string;
  licensePlate: string;
  chargingStatus: boolean;
  batteryLevel: number;
  currentSpeed: number;
  ts: string;
  lon: number;
  lat: number;
  location: string;
}

// 车辆服务类
export class VehicleService {
  private websocket: WebSocket | null = null;
  private vehicleUpdateCallback: ((data: VehicleRealtimeStatus) => void) | null = null;
  private websocketUrl: string;
  private reconnectTimer: number | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(websocketUrl: string) {
    this.websocketUrl = websocketUrl;
  }

  // 连接WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 如果已经存在连接，先关闭
        if (this.websocket) {
          this.disconnect();
        }

        console.log("正在连接WebSocket:", this.websocketUrl);
        this.websocket = new WebSocket(this.websocketUrl);

        // 连接成功
        this.websocket.onopen = () => {
          console.log("WebSocket连接成功");
          this.reconnectAttempts = 0; // 重置重连计数
          resolve();
        };

        // 收到消息
        this.websocket.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            if (this.vehicleUpdateCallback) {
              this.vehicleUpdateCallback(data);
            }
          } catch (error) {
            console.error("解析WebSocket消息失败:", error);
          }
        };

        // 连接关闭
        this.websocket.onclose = (event) => {
          console.log(`WebSocket连接关闭: ${event.code} - ${event.reason}`);
          this.websocket = null;
          this.attemptReconnect();
        };

        // 连接错误
        this.websocket.onerror = (error) => {
          console.error("WebSocket连接错误:", error);
          reject(error);
        };
      } catch (error) {
        console.error("建立WebSocket连接失败:", error);
        reject(error);
      }
    });
  }

  // 尝试重新连接
  private attemptReconnect(): void {
    // 清除之前的重连计时器
    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // 检查是否达到最大重连次数
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log("达到最大重连次数，不再尝试重连");
      return;
    }

    // 增加重连计数
    this.reconnectAttempts++;

    // 设置重连延迟，使用指数退避策略
    const delay = Math.min(5000, 1000 * Math.pow(2, this.reconnectAttempts - 1));
    console.log(`将在 ${delay}ms 后尝试重新连接 (尝试 ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    this.reconnectTimer = window.setTimeout(() => {
      console.log("正在尝试重新连接...");
      this.connect().catch(() => {
        // 重连失败，下次会自动再次尝试
      });
    }, delay);
  }

  // 断开WebSocket连接
  disconnect(): void {
    // 清除重连计时器
    if (this.reconnectTimer !== null) {
      window.clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }

    // 关闭WebSocket连接
    if (this.websocket) {
      this.websocket.onclose = null; // 防止触发自动重连
      this.websocket.onerror = null;
      this.websocket.onmessage = null;
      this.websocket.onopen = null;

      // 如果连接已打开，则正常关闭
      if (this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.close(1000, "客户端主动断开连接");
      }
      this.websocket = null;
    }

    console.log("WebSocket连接已断开");
  }

  // 设置车辆更新回调函数
  onVehicleUpdate(callback: (data: VehicleRealtimeStatus) => void): void {
    this.vehicleUpdateCallback = callback;
  }

  // 检查连接状态
  isConnected(): boolean {
    return !!this.websocket && this.websocket.readyState === WebSocket.OPEN;
  }
}

// 创建单例实例
const vehicleService = new VehicleService('ws://localhost:10001/raw-ws');

export default vehicleService; 