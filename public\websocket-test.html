<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>车辆WebSocket连接测试</title>
  <style>
    body {
      font-family: 'Microsoft YaHei', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: #fff;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    h1 {
      color: #1890ff;
      text-align: center;
    }
    .control-panel {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f0f8ff;
      border-radius: 4px;
    }
    .log-panel {
      height: 300px;
      overflow-y: auto;
      border: 1px solid #e8e8e8;
      padding: 10px;
      background-color: #fafafa;
      font-family: monospace;
      margin-bottom: 20px;
    }
    .log-entry {
      margin-bottom: 5px;
      padding: 5px;
      border-bottom: 1px solid #f0f0f0;
    }
    .log-entry.error {
      color: #ff4d4f;
    }
    .log-entry.success {
      color: #52c41a;
    }
    .log-entry.info {
      color: #1890ff;
    }
    .button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin-right: 10px;
    }
    .button:hover {
      background-color: #40a9ff;
    }
    .button:disabled {
      background-color: #d9d9d9;
      cursor: not-allowed;
    }
    .disconnect {
      background-color: #ff4d4f;
    }
    .disconnect:hover {
      background-color: #ff7875;
    }
    .vehicle-data {
      display: flex;
      flex-wrap: wrap;
      margin-top: 20px;
    }
    .vehicle-card {
      width: calc(33.33% - 20px);
      margin: 10px;
      padding: 15px;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      background-color: #f9f9f9;
    }
    .vehicle-header {
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 10px;
      color: #001529;
    }
    .vehicle-info {
      font-size: 14px;
      line-height: 1.5;
    }
    .vehicle-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    .status-tag {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 12px;
      margin-left: 5px;
    }
    .status-online {
      background-color: #d9f7be;
      color: #389e0d;
    }
    .status-charging {
      background-color: #fff7e6;
      color: #fa8c16;
    }
    .status-offline {
      background-color: #f5f5f5;
      color: #8c8c8c;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>车辆WebSocket连接测试</h1>
    
    <div class="control-panel">
      <div>
        <label for="serverUrl">服务器地址:</label>
        <input type="text" id="serverUrl" value="ws://localhost:10001/raw-ws" style="width: 300px; margin: 5px 0 15px 0; padding: 5px;">
      </div>
      
      <button id="connectBtn" class="button">连接到服务器</button>
      <button id="disconnectBtn" class="button disconnect" disabled>断开连接</button>
    </div>
    
    <h2>连接日志</h2>
    <div id="logPanel" class="log-panel"></div>
    
    <h2>车辆数据</h2>
    <div id="vehicleData" class="vehicle-data">
      <div class="vehicle-card">
        <div class="vehicle-header">暂无数据</div>
        <div class="vehicle-info">连接后将显示车辆信息</div>
      </div>
    </div>
  </div>

  <script>
    // 存储DOM元素引用
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const serverUrlInput = document.getElementById('serverUrl');
    const logPanel = document.getElementById('logPanel');
    const vehicleDataContainer = document.getElementById('vehicleData');

    // 存储车辆数据
    const vehicles = new Map();
    
    // WebSocket
    let socket = null;
    let reconnectTimer = null;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;

    // 添加日志条目
    function log(message, type = 'info') {
      const logEntry = document.createElement('div');
      logEntry.className = `log-entry ${type}`;
      
      const timestamp = new Date().toLocaleTimeString();
      logEntry.textContent = `[${timestamp}] ${message}`;
      
      logPanel.appendChild(logEntry);
      logPanel.scrollTop = logPanel.scrollHeight;
    }

    // 连接到WebSocket服务器
    function connect() {
      const serverUrl = serverUrlInput.value.trim();
      
      if (!serverUrl) {
        log('服务器地址不能为空', 'error');
        return;
      }
      
      try {
        log(`尝试连接到 ${serverUrl}...`);
        
        // 创建WebSocket连接
        socket = new WebSocket(serverUrl);
        
        // 连接打开
        socket.onopen = function() {
          log('连接成功', 'success');
          
          // 更新UI状态
          connectBtn.disabled = true;
          disconnectBtn.disabled = false;
          serverUrlInput.disabled = true;
          
          // 重置重连计数
          reconnectAttempts = 0;
        };
        
        // 接收消息
        socket.onmessage = function(event) {
          try {
            const data = JSON.parse(event.data);
            handleVehicleUpdate(data);
          } catch (error) {
            log(`消息解析错误: ${error.message}`, 'error');
          }
        };
        
        // 连接关闭
        socket.onclose = function(event) {
          log(`连接关闭: ${event.code} - ${event.reason}`, 'error');
          resetConnectionState();
          attemptReconnect();
        };
        
        // 连接错误
        socket.onerror = function(error) {
          log(`连接错误`, 'error');
        };
      } catch (error) {
        log(`连接异常: ${error.message}`, 'error');
        resetConnectionState();
      }
    }
    
    // 尝试重新连接
    function attemptReconnect() {
      // 清除之前的重连计时器
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
      }
      
      // 检查是否达到最大重连次数
      if (reconnectAttempts >= maxReconnectAttempts) {
        log(`达到最大重连次数 (${maxReconnectAttempts})，不再尝试重连`, 'error');
        return;
      }
      
      // 增加重连计数
      reconnectAttempts++;
      
      // 使用指数退避策略计算延迟
      const delay = Math.min(5000, 1000 * Math.pow(2, reconnectAttempts - 1));
      log(`将在 ${delay}ms 后尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})`, 'info');
      
      // 设置重连计时器
      reconnectTimer = setTimeout(function() {
        log(`正在尝试重连...`, 'info');
        connect();
      }, delay);
    }

    // 断开WebSocket连接
    function disconnect() {
      // 清除重连计时器
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
      }
      
      if (socket) {
        // 移除事件监听器
        socket.onclose = null;
        socket.onerror = null;
        socket.onmessage = null;
        socket.onopen = null;
        
        // 如果连接已打开，则关闭
        if (socket.readyState === WebSocket.OPEN) {
          socket.close(1000, "客户端主动断开连接");
        }
        socket = null;
        log('已断开连接', 'info');
      }
      
      resetConnectionState();
    }

    // 重置连接状态
    function resetConnectionState() {
      connectBtn.disabled = false;
      disconnectBtn.disabled = true;
      serverUrlInput.disabled = false;
    }

    // 处理车辆更新消息
    function handleVehicleUpdate(data) {
      log(`收到车辆更新: ${data.licensePlate} (${data.deviceName})`, 'info');
      
      // 更新车辆数据
      vehicles.set(data.deviceName, {
        ...data,
        lastUpdated: new Date()
      });
      
      // 更新UI
      updateVehicleUI();
    }

    // 更新车辆UI
    function updateVehicleUI() {
      // 清空容器
      vehicleDataContainer.innerHTML = '';
      
      if (vehicles.size === 0) {
        const placeholderCard = document.createElement('div');
        placeholderCard.className = 'vehicle-card';
        placeholderCard.innerHTML = `
          <div class="vehicle-header">暂无数据</div>
          <div class="vehicle-info">等待车辆数据...</div>
        `;
        vehicleDataContainer.appendChild(placeholderCard);
        return;
      }
      
      // 添加每个车辆的卡片
      for (const [deviceName, vehicle] of vehicles.entries()) {
        const card = document.createElement('div');
        card.className = 'vehicle-card';
        
        // 状态样式
        const statusClass = vehicle.chargingStatus ? 'status-charging' : 'status-online';
        const statusText = vehicle.chargingStatus ? '充电中' : '在线';
        
        // 格式化时间
        const updateTime = vehicle.lastUpdated.toLocaleTimeString();
        
        card.innerHTML = `
          <div class="vehicle-header">
            ${vehicle.licensePlate} 
            <span class="status-tag ${statusClass}">${statusText}</span>
          </div>
          <div class="vehicle-info">
            <div class="vehicle-row">
              <span>设备名称:</span>
              <span>${vehicle.deviceName}</span>
            </div>
            <div class="vehicle-row">
              <span>电量:</span>
              <span>${vehicle.batteryLevel}%</span>
            </div>
            <div class="vehicle-row">
              <span>速度:</span>
              <span>${vehicle.currentSpeed} km/h</span>
            </div>
            <div class="vehicle-row">
              <span>位置:</span>
              <span>${vehicle.lon.toFixed(6)}, ${vehicle.lat.toFixed(6)}</span>
            </div>
            <div class="vehicle-row">
              <span>最后更新:</span>
              <span>${updateTime}</span>
            </div>
          </div>
        `;
        
        vehicleDataContainer.appendChild(card);
      }
    }

    // 添加事件监听器
    connectBtn.addEventListener('click', connect);
    disconnectBtn.addEventListener('click', disconnect);

    // 初始化
    log('页面已加载，请点击"连接到服务器"按钮开始测试');
  </script>
</body>
</html> 