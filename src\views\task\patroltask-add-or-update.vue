<template>
  <el-dialog
    :title="!dataForm.id ? '新增任务' : '修改任务'"
    v-model="visible"
    :close-on-click-modal="false"
    width="65%"
  >
    <el-form :model="dataForm" :rules="dataRule" ref="dataFormRef" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务名称" prop="taskName">
            <el-input v-model="dataForm.taskName" placeholder="任务名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="任务类型" prop="taskType">
            <el-select
              v-model="dataForm.taskType"
              placeholder="请选择任务类型"
              style="width: 100%"
              clearable
            >
              <el-option
                v-for="item in taskTypes"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
              <template #footer>
                <el-button
                  v-if="!isAdding"
                  type="primary"
                  text
                  size="small"
                  @click="onAddOption"
                >
                  增加任务类型
                </el-button>
                <template v-else>
                  <div style="display: flex; align-items: center; padding: 8px 0">
                    <el-input
                      v-model="optionName"
                      class="option-input"
                      placeholder="请输入任务类型名称"
                      size="small"
                      @keyup.enter="onConfirm"
                    />
                    <el-button type="primary" size="small" @click="onConfirm"
                      >确认</el-button
                    >
                    <el-button size="small" @click="clear">取消</el-button>
                  </div>
                </template>
              </template>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="执行车辆" prop="vehicleIds">
            <el-select
              v-model="dataForm.vehicleIds"
              placeholder="请选择执行车辆"
              multiple
              style="width: 100%"
            >
              <el-option
                v-for="item in carOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业路线" prop="plannedRoute">
            <el-select
              v-model="dataForm.plannedRoute"
              placeholder="请选择作业路线"
              style="width: 100%"
            >
              <el-option
                v-for="item in routeOptions"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="dataForm.startTime"
              type="datetime"
              placeholder="开始时间"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="dataForm.endTime"
              type="datetime"
              placeholder="结束时间"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="路线规划" prop="plannedRoute">
            <div class="route-map-container">
              <div id="mapContainer" class="map-container"></div>
              <div class="map-controls">
                <el-switch
                  v-model="trafficLayerVisible"
                  active-text="实时交通"
                  @change="toggleTrafficLayer"
                />
                <el-button size="small" type="primary" @click="startDrawRoute">
                  开始绘制路线
                </el-button>
                <el-button
                  size="small"
                  v-if="isDrawing"
                  type="danger"
                  @click="endDrawRoute"
                >
                  完成绘制
                </el-button>
                <el-button
                  size="small"
                  type="warning"
                  @click="clearRoute"
                  :disabled="!dataForm.plannedRoute"
                >
                  清除路线
                </el-button>
              </div>

              <!-- 路线信息区域 -->
              <div class="route-info" v-if="routeInfo.pointCount > 0">
                <div class="route-info-title">路线信息</div>
                <div class="route-info-item">
                  <span>路线点数：</span>
                  <span>{{ routeInfo.pointCount }}</span>
                </div>
                <div class="route-info-item">
                  <span>路线长度：</span>
                  <span>{{ routeInfo.distance }}</span>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="任务描述" prop="description">
            <el-input
              v-model="dataForm.description"
              type="textarea"
              :rows="3"
              placeholder="请输入任务描述"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="dataFormSubmit()">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, toRefs, onMounted, onBeforeUnmount, nextTick, watch } from "vue";
import { ElMessage } from "element-plus";
import type { CheckboxValueType } from "element-plus";
import AMapLoader from "@amap/amap-jsapi-loader";
import baseService from "@/service/baseService";

// 修复AMap类型声明
declare global {
  interface Window {
    _AMapSecurityConfig: {
      securityJsCode: string;
    };
    AMap: any; // 使用any类型避免TypeScript错误
  }
}

const isAdding = ref(false);
const value = ref<CheckboxValueType[]>([]);
const optionName = ref("");

// 表单数据
const dataForm = reactive<{
  id: number;
  taskNumber: string;
  taskName: string;
  taskType: string;
  vehicleIds: string[];
  startTime: string | Date;
  endTime: string | Date;
  plannedRoute: string;
  description: string;
  taskStatus: string;
  progress: number;
}>({
  id: 0,
  taskNumber: "",
  taskName: "",
  taskType: "",
  vehicleIds: [],
  startTime: "",
  endTime: "",
  plannedRoute: "",
  description: "",
  taskStatus: "未开始",
  progress: 0,
});

// 表单校验规则
const dataRule = reactive({
  taskName: [{ required: true, message: "任务名称不能为空", trigger: "blur" }],
  taskType: [{ required: true, message: "任务类型不能为空", trigger: "change" }],
  vehicleIds: [{ required: true, message: "执行车辆不能为空", trigger: "change" }],
  startTime: [{ required: true, message: "开始时间不能为空", trigger: "change" }],
  endTime: [{ required: true, message: "结束时间不能为空", trigger: "change" }],
});

// 对话框显示状态
const visible = ref(false);
const dataFormRef = ref();
const routeOptions = ref<Array<{ label: string; value: string }>>([]); // 用于存储路线选项
const carOptions = ref<Array<{ label: string; value: string }>>([]); // 用于在线车辆的存储
// 地图相关变量
const map = ref<any>(null);
const trafficLayer = ref<any>(null);
const trafficLayerVisible = ref(false);
const mouseTool = ref<any>(null);
const isDrawing = ref(false);
const routePath = ref([]);

// 添加路线信息状态
const routeInfo = reactive({
  pointCount: 0,
  distance: "0米",
  duration: "0分钟",
});

// 添加visible监听，确保对话框打开时加载地图
const visibleChanged = async (newVal: boolean) => {
  if (newVal && !map.value) {
    // 延迟一点点，确保DOM已渲染
    setTimeout(async () => {
      await initMap();

      // 如果是编辑模式且有路线数据，初始化路线
      if (dataForm.id && dataForm.plannedRoute) {
        initRouteData();
      }
    }, 200);
  }
};

// 监听visible变化
watch(visible, visibleChanged);

// 监听路线选择变化，实时在地图上显示
watch(
  () => dataForm.plannedRoute,
  (newValue) => {
    if (newValue && map.value) {
      // 清除当前地图上的路线
      map.value.clearMap();

      // 加载并显示新选择的路线
      showSelectedRoute(newValue);
    }
  },
  { immediate: false }
);

// 显示选中的路线
const showSelectedRoute = (routeJsonStr) => {
  if (!map.value || !routeJsonStr) return;

  try {
    // 解析路线数据
    const geoJson = JSON.parse(routeJsonStr);
    let pathCoordinates = [];

    // 处理不同格式的GeoJSON数据
    if (geoJson.type === "Feature" && geoJson.geometry?.coordinates) {
      // 标准GeoJSON Feature格式
      pathCoordinates = geoJson.geometry.coordinates;
    } else if (geoJson.coordinates && Array.isArray(geoJson.coordinates)) {
      // 简化的GeoJSON格式
      pathCoordinates = geoJson.coordinates;
    } else if (Array.isArray(geoJson)) {
      // 直接的坐标数组
      pathCoordinates = geoJson;
    } else {
      throw new Error("无效的路线数据格式");
    }

    if (pathCoordinates.length > 0) {
      const AMap = window.AMap;

      // 创建折线
      const polyline = new AMap.Polyline({
        path: pathCoordinates,
        strokeColor: "#28F",
        strokeOpacity: 0.8,
        strokeWeight: 6,
        showDir: true,
      });

      // 标记起点和终点
      const startMarker = new AMap.Marker({
        position: pathCoordinates[0],
        content: '<div class="route-point start-point">起</div>',
        offset: new AMap.Pixel(-10, -10),
      });

      const endMarker = new AMap.Marker({
        position: pathCoordinates[pathCoordinates.length - 1],
        content: '<div class="route-point end-point">终</div>',
        offset: new AMap.Pixel(-10, -10),
      });

      // 添加到地图
      map.value.add([polyline, startMarker, endMarker]);

      // 调整视图以适应路线
      map.value.setFitView();

      // 更新路线信息
      const formattedPath = pathCoordinates.map((p) => ({ lng: p[0], lat: p[1] }));
      routePath.value = formattedPath;
      routeInfo.pointCount = formattedPath.length;
      routeInfo.distance = calculateRouteDistance(formattedPath);
    }
  } catch (error) {
    console.error("显示选中路线失败:", error);
    ElMessage.error("路线数据格式错误，无法显示");
  }
};

// 提交表单
const dataFormSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      (!dataForm.id ? baseService.post : baseService.put)(
        "/task/patroltask",
        dataForm
      ).then((res) => {
        ElMessage.success({
          message: `${!dataForm.id ? "新增" : "修改"}任务成功！`,
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          },
        });
      });
    }
  });
};
// 任务类型选择框
const taskTypes = ref([
  { value: "智能巡逻任务", label: "智能巡逻任务" },
  { value: "园区安防巡检", label: "园区安防巡检" },
  { value: "夜间巡逻任务", label: "夜间巡逻任务" },
]);
const onAddOption = () => {
  isAdding.value = true;
  setTimeout(() => {
    const inputEl = document.querySelector(".option-input");
    if (inputEl) {
      (inputEl as HTMLInputElement).focus();
    }
  }, 10);
  console.log("isAdding 状态:", isAdding.value);
};

const onConfirm = () => {
  if (optionName.value.trim()) {
    taskTypes.value.push({
      value: optionName.value,
      label: optionName.value,
    });
    // 自动选中新添加的选项
    dataForm.taskType = optionName.value;
    clear();
  } else {
    ElMessage({
      message: "请输入任务类型名称",
      type: "warning",
      duration: 2000,
    });
  }
};

const clear = () => {
  optionName.value = "";
  isAdding.value = false;
};

// 初始化地图
const initMap = async () => {
  try {
    // 安全配置
    window._AMapSecurityConfig = {
      securityJsCode: "a9676a96467adef5269a629f780bdb07",
    };

    const AMap = await AMapLoader.load({
      key: "eea9447f9cd0ad60cd7a7b612974f3fa", // API密钥
      version: "2.0",
      plugins: [
        "AMap.Scale",
        "AMap.MouseTool",
        "AMap.MoveAnimation",
        "AMap.ToolBar",
        // 多种可能的交通图层插件名称
        "AMap.TileLayer.Traffic",
        "AMap.Traffic",
      ],
    });

    // 保存AMap到全局，方便后续使用
    window.AMap = AMap;

    // 创建地图实例
    map.value = new AMap.Map("mapContainer", {
      viewMode: "3D",
      zoom: 15,
      center: [104.002052, 30.713956], // 默认中心点坐标
      mapStyle: "amap://styles/darkblue", // 暗黑主题
    });

    // 添加控件
    if (map.value) {
      map.value.addControl(new AMap.Scale());
      map.value.addControl(new AMap.ToolBar());
    }

    // 尝试创建交通图层 - 方法1
    try {
      trafficLayer.value = new AMap.TileLayer.Traffic({
        zIndex: 10,
        opacity: 0.8,
      });
      console.log("成功创建交通图层 (TileLayer.Traffic)");
    } catch (e) {
      console.warn("方法1创建交通图层失败:", e);

      // 备选方法 - 方法2
      try {
        trafficLayer.value = new AMap.Traffic({
          zIndex: 10,
        });
        console.log("成功创建交通图层 (Traffic)");
      } catch (e2) {
        console.warn("方法2创建交通图层失败:", e2);

        // 备选方法 - 方法3 (2.0版本可能的写法)
        try {
          const TrafficLayer = AMap.createDefaultLayer
            ? AMap.createDefaultLayer("traffic")
            : new AMap.TileLayer.Traffic();

          trafficLayer.value = TrafficLayer;
          console.log("成功创建交通图层 (备选方法3)");
        } catch (e3) {
          console.error("所有创建交通图层的方法均失败:", e3);
          trafficLayer.value = null;
          ElMessage.warning("交通图层功能不可用");
        }
      }
    }

    // 创建鼠标工具
    mouseTool.value = new AMap.MouseTool(map.value);

    // 监听绘制完成事件
    if (mouseTool.value) {
      mouseTool.value.on("draw", function (e: any) {
        if (e && e.obj && typeof e.obj.getPath === "function") {
          // 获取绘制的路径点
          const path = e.obj.getPath();

          // 格式化为标准经纬度数组
          const formattedPath = path.map((point: any) => {
            return {
              lng: point.getLng(),
              lat: point.getLat(),
            };
          });

          // 保存到组件状态和表单数据
          routePath.value = formattedPath;

          // 计算路线长度
          const distance = calculateRouteDistance(formattedPath);

          // 更新路线信息
          routeInfo.pointCount = formattedPath.length;
          routeInfo.distance = distance;

          // 将对象数组转为JSON字符串保存到表单
          dataForm.plannedRoute = JSON.stringify({
            coordinates: formattedPath.map((p) => [p.lng, p.lat]),
          });

          isDrawing.value = false;

          // 显示保存成功和路线点数提示
          ElMessage({
            message: `路线规划完成，共设置了${formattedPath.length}个路线点，长度约${distance}`,
            type: "success",
            duration: 2000,
          });

          console.log("路线数据已保存到表单:", dataForm.plannedRoute);
        }
      });
    }

    return true;
  } catch (error) {
    console.error("地图初始化失败:", error);
    ElMessage.error("地图加载失败，请刷新重试");
    return false;
  }
};

// 切换实时交通图层
const toggleTrafficLayer = () => {
  if (!map.value || !trafficLayer.value) return;

  try {
    if (trafficLayerVisible.value) {
      // 显示交通图层
      if (!trafficLayer.value.getMap()) {
        trafficLayer.value.setMap(map.value);
      }
      ElMessage({
        message: "已开启实时交通图层",
        type: "success",
        duration: 1500,
      });
    } else {
      // 隐藏交通图层
      trafficLayer.value.setMap(null);
      ElMessage({
        message: "已关闭实时交通图层",
        type: "info",
        duration: 1500,
      });
    }
  } catch (error) {
    console.error("切换交通图层失败:", error);
    ElMessage.error("交通图层操作失败");
    // 重置开关状态
    trafficLayerVisible.value = !trafficLayerVisible.value;
  }
};

// 开始绘制路线
const startDrawRoute = () => {
  if (!mouseTool.value || !map.value) return;

  // 清除已有路线
  map.value.clearMap();

  // 开始绘制折线
  mouseTool.value.polyline({
    strokeColor: "#28F",
    strokeOpacity: 0.8,
    strokeWeight: 6,
  });

  isDrawing.value = true;

  ElMessage({
    message: "请在地图上点击绘制路线，双击结束",
    type: "info",
    duration: 3000,
  });
};

// 结束绘制路线
const endDrawRoute = () => {
  if (!mouseTool.value) return;
  if (typeof mouseTool.value.close === "function") {
    mouseTool.value.close(true);
  }
  isDrawing.value = false;
};

// 添加清除路线的方法
const clearRoute = () => {
  if (!map.value) return;

  // 清除地图上的覆盖物
  map.value.clearMap();

  // 清除路线数据
  routePath.value = [];
  dataForm.plannedRoute = "";

  // 重置路线信息
  routeInfo.pointCount = 0;
  routeInfo.distance = "0米";
  routeInfo.duration = "0分钟";

  ElMessage({
    message: "已清除路线规划",
    type: "info",
    duration: 1500,
  });
};

// 初始化已有路线数据
const initRouteData = () => {
  if (!map.value || !dataForm.plannedRoute) return;

  try {
    const routeData = JSON.parse(dataForm.plannedRoute);
    let pathCoordinates: any[] = [];

    // 支持两种数据格式：旧格式的坐标数组和新格式的GeoJSON结构
    if (routeData.coordinates && Array.isArray(routeData.coordinates)) {
      // 新格式 - GeoJSON 结构
      pathCoordinates = routeData.coordinates;
    } else if (Array.isArray(routeData)) {
      // 旧格式 - 直接的坐标数组
      pathCoordinates = routeData;
    } else {
      throw new Error("无效的路线数据格式");
    }

    if (pathCoordinates.length > 0) {
      // 创建折线
      const AMap = window.AMap;
      if (!AMap) {
        console.error("AMap不可用");
        return;
      }

      const polyline = new AMap.Polyline({
        path: pathCoordinates,
        strokeColor: "#28F",
        strokeOpacity: 0.8,
        strokeWeight: 6,
        // 添加箭头指向
        showDir: true,
      });

      // 显示路线点位标记
      if (pathCoordinates.length > 1) {
        // 标记起点和终点
        const startMarker = new AMap.Marker({
          position: pathCoordinates[0],
          content: '<div class="route-point start-point">起</div>',
          offset: new AMap.Pixel(-10, -10),
        });

        const endMarker = new AMap.Marker({
          position: pathCoordinates[pathCoordinates.length - 1],
          content: '<div class="route-point end-point">终</div>',
          offset: new AMap.Pixel(-10, -10),
        });

        map.value.add([polyline, startMarker, endMarker]);
      } else {
        map.value.add(polyline);
      }

      map.value.setFitView();

      // 更新routePath为最新数据
      routePath.value = pathCoordinates.map((p) => ({ lng: p[0], lat: p[1] }));

      ElMessage({
        message: `已加载路线，共${pathCoordinates.length}个路线点`,
        type: "success",
        duration: 1500,
      });
    }
  } catch (error) {
    console.error("路线数据解析失败:", error);
    ElMessage.error("路线数据格式错误，无法显示");
  }
};

// 计算路线长度的函数
const calculateRouteDistance = (path: any[]): string => {
  if (!window.AMap || path.length < 2) return "0米";

  try {
    const len = path.length;
    let distance = 0;

    for (let i = 0; i < len - 1; i++) {
      // 使用高德地图API计算两点间距离
      const p1 = path[i];
      const p2 = path[i + 1];

      if (typeof window.AMap.GeometryUtil?.distance === "function") {
        // 优先使用GeometryUtil计算精确距离
        const segmentDist = window.AMap.GeometryUtil.distance(
          [p1.lng || p1[0], p1.lat || p1[1]],
          [p2.lng || p2[0], p2.lat || p2[1]]
        );
        distance += segmentDist;
      } else {
        // 备用方案：使用简单的球面距离计算
        const lnglat1 = new window.AMap.LngLat(p1.lng || p1[0], p1.lat || p1[1]);
        const lnglat2 = new window.AMap.LngLat(p2.lng || p2[0], p2.lat || p2[1]);
        distance += lnglat1.distance(lnglat2);
      }
    }

    // 格式化距离显示
    if (distance > 1000) {
      return `${(distance / 1000).toFixed(2)}公里`;
    } else {
      return `${Math.round(distance)}米`;
    }
  } catch (error) {
    console.error("计算路线长度失败:", error);
    return "计算错误";
  }
};
const getInfo = (id: number) => {
  baseService.get("/task/patroltask/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};
// 获取巡逻路线数据
const getPatrolRoutes = () => {
  baseService
    .get("/task/patrolroute/list")
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data)) {
        // 解析每个GeoJSON字符串
        routeOptions.value = res.data.map((jsonStr) => {
          try {
            const geoJson = JSON.parse(jsonStr);
            const routeName = geoJson?.properties?.routeName || "未命名路线";
            return {
              label: routeName,
              value: jsonStr, // 整个GeoJSON字符串作为值
            };
          } catch (err) {
            console.error("解析路线数据错误:", err);
            return {
              label: "错误的路线数据",
              value: jsonStr,
            };
          }
        });

        // 如果当前已有选中的路线，则显示它
        if (dataForm.plannedRoute && map.value) {
          nextTick(() => {
            showSelectedRoute(dataForm.plannedRoute);
          });
        }
        // 如果没有选中的路线但有可选路线，自动选择第一条（可选）
        else if (routeOptions.value.length > 0 && !dataForm.plannedRoute) {
          // 取消自动选择第一条路线的注释即可启用此功能
          // dataForm.plannedRoute = routeOptions.value[0].value;
        }
      } else {
        console.error("获取路线数据失败或数据格式不正确");
        routeOptions.value = [];
      }
    })
    .catch((err) => {
      console.error("获取路线数据请求失败:", err);
      routeOptions.value = [];
    });
};
// 获取在线车辆
const getOnlineCar = () => {
  baseService
    .get("/task/patroltask/onlinecar")
    .then((res) => {
      if (res.code === 0 && Array.isArray(res.data)) {
        // 清空现有车辆选项
        carOptions.value = [];

        // 遍历返回的数据并转换为下拉选项格式
        res.data.forEach((carItem) => {
          // 对于每个车辆对象 {id: carName}
          Object.entries(carItem).forEach(([id, carName]) => {
            carOptions.value.push({
              label: carName, // 显示车辆名称
              value: id, // 值为车辆ID
            });
          });
        });

        console.log("车辆选项已更新:", carOptions.value);
      } else {
        console.error("获取在线车辆数据失败或格式不正确:", res);
        carOptions.value = [];
      }
    })
    .catch((err) => {
      console.error("获取在线车辆请求失败:", err);
      carOptions.value = [];
    });
};
// 正确的init函数实现
const init = (id?: number) => {
  visible.value = true;

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  dataForm.id = 0; // 使用数字0替代空字符串

  // 表单数据重置
  dataForm.taskName = "";
  dataForm.taskType = "";
  dataForm.vehicleIds = [];
  dataForm.startTime = "";
  dataForm.endTime = "";
  dataForm.plannedRoute = "";
  dataForm.description = "";
  dataForm.taskStatus = "未开始";
  dataForm.progress = 0;

  // 根据ID获取数据
  if (id) {
    dataForm.id = id;
    getInfo(id);
  }

  // 初始化地图后再加载路线数据
  nextTick(() => {
    initMap()
      .then(() => {
        // 地图初始化完成后获取路线数据
        getPatrolRoutes();
        // 获取在线车辆信息
        getOnlineCar();

        // 如果是编辑模式并且有路线数据，显示当前路线
        if (id && dataForm.plannedRoute) {
          setTimeout(() => {
            showSelectedRoute(dataForm.plannedRoute);
          }, 300);
        }
      })
      .catch((err) => {
        console.error("地图初始化失败:", err);
      });
  });
};

// 清理地图资源
onBeforeUnmount(() => {
  if (map.value && typeof map.value.destroy === "function") {
    map.value.destroy();
  }
});

// 事件
const emit = defineEmits(["refreshDataList"]);

// 对外暴露方法
defineExpose({
  init,
});

// 在组件加载时重置状态
onMounted(() => {
  isAdding.value = false;
  optionName.value = "";
});
</script>

<style lang="less" scoped>
.route-map-container {
  width: 100%;
  height: 280px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;

  .map-container {
    width: 100%;
    height: 100%;
  }

  .map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    background: rgba(255, 255, 255, 0.1);
    padding: 10px;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    max-width: 280px;
  }
}

.option-input {
  width: 200px;
  margin-right: 8px;
}

:deep(.el-select__footer) {
  padding: 5px 12px;
  border-top: 1px solid #ebeef5;
}

/* 移除原有的地图占位符样式 */
.map-placeholder {
  display: none;
}

// 添加路线点标记样式
:deep(.route-point) {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
}

:deep(.start-point) {
  background-color: #1bbc9b;
}

:deep(.end-point) {
  background-color: #e74c3c;
}

// 添加路线规划的预览信息样式
.route-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  max-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 4px;

  .route-info-title {
    font-weight: bold;
    margin-bottom: 2px;
  }

  .route-info-item {
    display: flex;
    justify-content: space-between;
  }
}
</style>
