<template>
  <div class="patrol-video-system">
    <!-- 主要内容区域 -->
    <div class="main-container">
      <!-- 上部：视频预览和车辆选择 -->
      <div class="top-section">
        <!-- 视频预览区域 -->
        <div class="preview-panel">
          <div class="panel-header">
            <h2>视频预览</h2>
          </div>

          <div v-if="selectedVideo" class="preview-content">
            <div class="video-player-wrapper">
              <video
                ref="videoPlayerRef"
                :src="selectedVideo.fileUrl"
                controls
                preload="metadata"
                class="video-player"
                @loadedmetadata="onVideoLoaded"
              >
                您的浏览器不支持视频播放。
              </video>
            </div>

            <div class="video-details">
              <h3>{{ selectedVideo.title }}</h3>
              <p class="video-description">{{ selectedVideo.description }}</p>

              <div class="video-meta">
                <span class="meta-item">
                  <el-icon><Clock /></el-icon>
                  时长：{{ formatDuration(selectedVideo.duration) }}
                </span>
                <span class="meta-item">
                  <el-icon><Document /></el-icon>
                  大小：{{ formatFileSize(selectedVideo.fileSize) }}
                </span>
                <span class="meta-item">
                  <el-icon><Calendar /></el-icon>
                  上传时间：{{ formatDateTime(selectedVideo.uploadTime) }}
                </span>
              </div>
            </div>
          </div>

          <div v-else class="empty-preview">
            <el-empty description="请从下方列表选择一个视频进行预览" :image-size="120" />
          </div>
        </div>

        <!-- 巡逻车辆选择区域 -->
        <div class="vehicle-panel">
          <div class="panel-header">
            <h2>巡逻车辆</h2>
            <el-button text type="primary" size="small" @click="selectAllVehicles">
              {{ selectedVehicles.length === vehicles.length ? "取消全选" : "全选" }}
            </el-button>
          </div>

          <div class="vehicle-grid">
            <div
              v-for="vehicle in vehicles"
              :key="vehicle.id"
              :class="['vehicle-item', { selected: isVehicleSelected(vehicle.id) }]"
              @click="toggleVehicleSelection(vehicle)"
            >
              <el-checkbox
                :model-value="isVehicleSelected(vehicle.id)"
                @click.stop
                @change="toggleVehicleSelection(vehicle)"
              />
              <div class="vehicle-icon">
                <el-icon size="20"><Van /></el-icon>
              </div>
              <div class="vehicle-info">
                <div class="vehicle-number">{{ vehicle.plateNumber }}</div>
                <div class="vehicle-model">{{ vehicle.model }}</div>
                <el-tag
                  :type="vehicle.status === 'online' ? 'success' : 'info'"
                  size="small"
                >
                  {{ vehicle.status === "online" ? "在线" : "离线" }}
                </el-tag>
              </div>
              <div class="current-video" v-if="vehicle.currentVideo">
                <span class="current-label">当前播放：</span>
                <span class="current-title" :title="vehicle.currentVideo.title">
                  {{ vehicle.currentVideo.title }}
                </span>
              </div>
            </div>
          </div>

          <!-- 分配按钮 -->
          <div class="assign-actions">
            <el-button
              type="primary"
              size="large"
              :disabled="!selectedVideo || selectedVehicles.length === 0"
              @click="assignVideoToVehicles"
            >
              <el-icon><Connection /></el-icon>
              分配视频到选中的车辆 ({{ selectedVehicles.length }})
            </el-button>
          </div>
        </div>
      </div>

      <!-- 下部：视频列表和车辆详情 -->
      <div class="bottom-section">
        <div class="video-list-panel">
          <div class="panel-header">
            <h2>视频列表</h2>
            <div class="header-actions">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索视频"
                style="width: 300px"
                @keyup.enter="handleSearch"
                clearable
                size="default"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button type="primary" @click="showUploadDialog">
                <el-icon><Upload /></el-icon>
                上传视频
              </el-button>
            </div>
          </div>

          <!-- 视频表格列表 -->
          <el-table
            :data="videoList"
            v-loading="loading"
            style="width: 100%; height: 300px"
            highlight-current-row
            @row-click="handleRowClick"
            :row-class-name="getRowClassName"
          >
            <el-table-column type="index" label="序号" width="60" />

            <el-table-column prop="title" label="视频标题" min-width="200">
              <template #default="{ row }">
                <span class="video-title-cell">
                  <el-icon><VideoPlay /></el-icon>
                  {{ row.title }}
                </span>
              </template>
            </el-table-column>

            <el-table-column
              prop="description"
              label="描述"
              min-width="300"
              show-overflow-tooltip
            />

            <el-table-column prop="duration" label="时长" width="100">
              <template #default="{ row }">
                {{ formatDuration(row.duration) }}
              </template>
            </el-table-column>

            <el-table-column prop="fileSize" label="文件大小" width="120">
              <template #default="{ row }">
                {{ formatFileSize(row.fileSize) }}
              </template>
            </el-table-column>

            <el-table-column prop="status" label="状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="uploadTime" label="上传时间" width="180">
              <template #default="{ row }">
                {{ formatDateTime(row.uploadTime) }}
              </template>
            </el-table-column>

            <el-table-column label="操作" width="100" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  circle
                  @click.stop="deleteVideo(row)"
                />
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[10, 20, 30, 50]"
              :total="total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传视频"
      width="600px"
      :before-close="handleUploadClose"
    >
      <el-form
        :model="uploadForm"
        :rules="uploadRules"
        ref="uploadFormRef"
        label-width="100px"
      >
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="uploadForm.title" placeholder="请输入视频标题" />
        </el-form-item>

        <el-form-item label="视频描述" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入视频描述"
          />
        </el-form-item>

        <el-form-item label="选择文件" prop="file">
          <el-upload
            class="upload-demo"
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            accept="video/*"
            :limit="1"
            drag
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip">
                支持 mp4、avi、mov 格式，文件大小不超过 500MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item v-if="uploadProgress > 0">
          <el-progress :percentage="uploadProgress" :status="uploadStatus" />
          <div class="upload-info">{{ uploadInfo }}</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="handleUploadClose">取消</el-button>
        <el-button
          type="primary"
          :loading="uploading"
          :disabled="!uploadForm.file"
          @click="handleUpload"
        >
          {{ uploading ? "上传中..." : "开始上传" }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Search,
  Upload,
  VideoPlay,
  Clock,
  Document,
  Calendar,
  Delete,
  UploadFilled,
  Van,
  Connection,
} from "@element-plus/icons-vue";

// 类型定义
interface Video {
  id: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  fileUrl: string;
  duration: number;
  fileSize: number;
  status: string;
  uploadTime: string;
}

interface Vehicle {
  id: string;
  plateNumber: string;
  model: string;
  status: "online" | "offline";
  currentVideo?: {
    id: string;
    title: string;
  };
}

// 响应式数据
const loading = ref(false);
const videoList = ref<Video[]>([]);
const selectedVideo = ref<Video | null>(null);
const selectedVehicles = ref<Vehicle[]>([]);
const vehicles = ref<Vehicle[]>([]);
const searchKeyword = ref("");
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 上传相关
const uploadDialogVisible = ref(false);
const uploadFormRef = ref();
const uploadRef = ref();
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref<"success" | "exception" | undefined>();
const uploadInfo = ref("");

const uploadForm = reactive({
  title: "",
  description: "",
  file: null as File | null,
});

const uploadRules = {
  title: [
    { required: true, message: "请输入视频标题", trigger: "blur" },
    { min: 2, max: 50, message: "标题长度在 2 到 50 个字符", trigger: "blur" },
  ],
  description: [
    { required: true, message: "请输入视频描述", trigger: "blur" },
    { min: 5, max: 200, message: "描述长度在 5 到 200 个字符", trigger: "blur" },
  ],
};

// 视频播放器引用
const videoPlayerRef = ref<HTMLVideoElement>();

// 生命周期
onMounted(() => {
  loadVideoList();
  loadVehicleList();
});

// 方法
const loadVideoList = async () => {
  loading.value = true;
  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟数据
    videoList.value = Array.from({ length: 20 }, (_, i) => ({
      id: `video-${i + 1}`,
      title: `宣传视频 ${i + 1}`,
      description: `这是第 ${i + 1} 个宣传视频的描述内容，包含了相关的宣传信息和播放说明`,
      thumbnailUrl: `/api/placeholder/320/180`,
      fileUrl: `/videos/sample-${i + 1}.mp4`,
      duration: Math.floor(Math.random() * 300) + 60,
      fileSize: Math.floor(Math.random() * 100000000) + 10000000,
      status: ["READY", "PROCESSING", "READY"][Math.floor(Math.random() * 3)],
      uploadTime: new Date(
        Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000
      ).toISOString(),
    }));

    total.value = 20;
  } catch (error) {
    ElMessage.error("加载视频列表失败");
  } finally {
    loading.value = false;
  }
};

const loadVehicleList = async () => {
  // 模拟加载车辆数据
  vehicles.value = [
    {
      id: "v1",
      plateNumber: "粤A88888",
      model: "依维柯",
      status: "online",
      currentVideo: { id: "video-1", title: "宣传视频 1" },
    },
    { id: "v2", plateNumber: "粤A66666", model: "福特全顺", status: "online" },
    { id: "v3", plateNumber: "粤A99999", model: "依维柯", status: "offline" },
    {
      id: "v4",
      plateNumber: "粤A12345",
      model: "江铃",
      status: "online",
      currentVideo: { id: "video-3", title: "宣传视频 3" },
    },
    { id: "v5", plateNumber: "粤A54321", model: "福特全顺", status: "offline" },
    { id: "v6", plateNumber: "粤A11111", model: "依维柯", status: "online" },
  ];
};

const handleRowClick = (row: Video) => {
  if (row.status !== "READY") {
    ElMessage.warning("该视频还在处理中，暂时无法预览");
    return;
  }
  selectedVideo.value = row;
};

const getRowClassName = ({ row }: { row: Video }) => {
  return selectedVideo.value?.id === row.id ? "selected-row" : "";
};

const isVehicleSelected = (vehicleId: string): boolean => {
  return selectedVehicles.value.some((v) => v.id === vehicleId);
};

const toggleVehicleSelection = (vehicle: Vehicle) => {
  const index = selectedVehicles.value.findIndex((v) => v.id === vehicle.id);
  if (index > -1) {
    selectedVehicles.value.splice(index, 1);
  } else {
    selectedVehicles.value.push(vehicle);
  }
};

const selectAllVehicles = () => {
  if (selectedVehicles.value.length === vehicles.value.length) {
    selectedVehicles.value = [];
  } else {
    selectedVehicles.value = [...vehicles.value];
  }
};

const assignVideoToVehicles = async () => {
  if (!selectedVideo.value || selectedVehicles.value.length === 0) {
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要将视频《${selectedVideo.value.title}》分配给选中的 ${selectedVehicles.value.length} 辆车吗？`,
      "确认分配",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      }
    );

    // 模拟API调用
    ElMessage.success("视频分配成功！");

    // 更新车辆当前视频
    selectedVehicles.value.forEach((vehicle) => {
      const v = vehicles.value.find((vh) => vh.id === vehicle.id);
      if (v) {
        v.currentVideo = {
          id: selectedVideo.value!.id,
          title: selectedVideo.value!.title,
        };
      }
    });

    // 清空选择
    selectedVehicles.value = [];
  } catch (error) {
    // 用户取消
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  loadVideoList();
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadVideoList();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadVideoList();
};

// 上传相关方法
const showUploadDialog = () => {
  uploadDialogVisible.value = true;
};

const handleFileChange = (file: any) => {
  uploadForm.file = file.raw;
  if (!uploadForm.title) {
    const fileName = file.name;
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
    uploadForm.title = nameWithoutExt;
  }
};

const beforeUpload = (file: File) => {
  const isVideo = file.type.startsWith("video/");
  const isLt500M = file.size / 1024 / 1024 < 500;

  if (!isVideo) {
    ElMessage.error("只能上传视频文件!");
    return false;
  }

  if (!isLt500M) {
    ElMessage.error("文件大小不能超过 500MB!");
    return false;
  }

  return true;
};

const handleUpload = async () => {
  if (!uploadFormRef.value) return;

  try {
    await uploadFormRef.value.validate();

    uploading.value = true;
    uploadProgress.value = 0;
    uploadStatus.value = undefined;
    uploadInfo.value = "准备上传...";

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10;
        if (uploadProgress.value < 30) {
          uploadInfo.value = "正在上传文件...";
        } else if (uploadProgress.value < 60) {
          uploadInfo.value = "文件上传中...";
        } else if (uploadProgress.value < 90) {
          uploadInfo.value = "正在处理视频...";
        }
      }
    }, 500);

    // 模拟上传
    await new Promise((resolve) => setTimeout(resolve, 3000));

    clearInterval(progressInterval);
    uploadProgress.value = 100;
    uploadStatus.value = "success";
    uploadInfo.value = "上传成功！";

    ElMessage.success("视频上传成功！");

    await loadVideoList();

    setTimeout(() => {
      handleUploadClose();
    }, 1000);
  } catch (error: any) {
    uploadStatus.value = "exception";
    uploadInfo.value = "上传失败：" + (error.message || "未知错误");
    ElMessage.error("上传失败，请重试");
  } finally {
    uploading.value = false;
  }
};

const handleUploadClose = () => {
  uploadForm.title = "";
  uploadForm.description = "";
  uploadForm.file = null;
  uploadProgress.value = 0;
  uploadStatus.value = undefined;
  uploadInfo.value = "";

  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }

  if (uploadFormRef.value) {
    uploadFormRef.value.resetFields();
  }

  uploadDialogVisible.value = false;
};

const deleteVideo = async (video: Video) => {
  try {
    await ElMessageBox.confirm(`确定要删除视频 "${video.title}" 吗？`, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    ElMessage.success("删除成功");
    await loadVideoList();
  } catch (error) {
    // 用户取消
  }
};

const onVideoLoaded = () => {
  console.log("Video loaded");
};

// 工具方法
const formatDuration = (seconds: number): string => {
  if (!seconds || seconds <= 0) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  } else {
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }
};

const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes <= 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return "";

  const date = new Date(dateTime);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  });
};

const getStatusType = (status: string): string => {
  switch (status) {
    case "READY":
      return "success";
    case "PROCESSING":
      return "warning";
    case "ERROR":
      return "danger";
    default:
      return "info";
  }
};

const getStatusText = (status: string): string => {
  switch (status) {
    case "READY":
      return "就绪";
    case "PROCESSING":
      return "处理中";
    case "ERROR":
      return "错误";
    default:
      return "未知";
  }
};
</script>

<style scoped>
.patrol-video-system {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #001528;
  overflow: auto;
  min-height: 100vh;
  color: #fff;
}

.page-header {
  background: white;
  padding: 20px 30px;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.page-header h1 {
  margin: 0 0 5px 0;
  font-size: 24px;
  color: #303133;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
  height: 100%;
  min-height: 900px;
  position: relative;
}

/* 上部区域 */
.top-section {
  display: flex;
  gap: 20px;
  padding: 20px 20px 10px;
  flex: 0 0 auto;
  max-height: 500px;
}

/* 视频预览面板 */
.preview-panel {
  width: calc(100% - 420px);
  background: #0a2140;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  max-height: 450px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
  color: #fff;
}

.preview-content {
  flex: 1;
  display: flex;
  gap: 20px;
}

.video-player-wrapper {
  flex: 1;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
  max-height: 300px;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-details {
  width: 300px;
  padding: 0 10px;
}

.video-details h3 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #fff;
}

.video-description {
  margin: 0 0 20px 0;
  color: #a6b5cc;
  font-size: 14px;
  line-height: 1.5;
}

.video-meta {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #a6b5cc;
  font-size: 14px;
}

.meta-item .el-icon {
  color: #409eff;
}

.empty-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

/* 车辆面板 */
.vehicle-panel {
  width: 400px;
  background: #0a2140;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  flex: 0 0 auto;
  min-height: 870px; /* 预览面板高度 + 底部区域最小高度 + 间距 */
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: absolute;
  right: 20px;
  top: 20px;
  bottom: 20px;
}

.vehicle-grid {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  overflow-y: auto;
  padding-right: 5px;
  margin-bottom: 15px;
  min-height: 400px;
  max-height: 640px;
}

.vehicle-item {
  background: #001528;
  border-radius: 8px;
  padding: 5px;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  gap: 10px;
}

.vehicle-item:hover {
  background: #0a3b66;
}

.vehicle-item.selected {
  border-color: #409eff;
  background: #0a3b66;
}

.vehicle-icon {
  width: 36px;
  height: 36px;
  background: #0a2140;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.vehicle-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
}

.vehicle-number {
  font-size: 16px;
  font-weight: 500;
  color: #fff;
}

.vehicle-model {
  font-size: 14px;
  color: #a6b5cc;
}

.current-video {
  font-size: 12px;
  color: #606266;
  margin-left: auto;
  max-width: 150px;
}

.current-label {
  color: #909399;
}

.current-title {
  color: #409eff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.assign-actions {
  padding: 15px 0;
  border-top: 1px solid #e4e7ed;
}

.assign-actions .el-button {
  width: 100%;
}

/* 下部区域 */
.bottom-section {
  flex: 1;
  padding: 20px;
  min-height: 400px;
  display: block;
}

.video-list-panel {
  width: calc(100% - 420px);
  height: 90%;
  background: #0a2140;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  flex: none;
  margin-right: 0;
  min-height: 400px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 表格样式 */
.video-title-cell {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
}

/* 表格容器
:deep(.el-table__inner-wrapper) {
  height: 800px;
}

.video-title-cell .el-icon {
  color: #409eff;
}

:deep(.el-table__row) {
  cursor: pointer;
}

:deep(.el-table__row:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table__row.selected-row) {
  background-color: #e6f1ff;
}

:deep(.el-table__row.selected-row:hover) {
  background-color: #d9e8ff;
} */

/* 分页 */
.pagination-wrapper {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

/* 上传相关 */
.upload-demo {
  width: 100%;
}

.upload-info {
  margin-top: 10px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c0c4cc;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #909399;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .video-details {
    width: 250px;
  }

  .vehicle-panel {
    width: 350px;
  }
}

@media (max-width: 1200px) {
  .top-section {
    flex-direction: column;
    max-height: none;
  }

  .bottom-section {
    flex-direction: column;
  }

  .vehicle-panel {
    width: 100%;
    max-height: 400px;
  }

  .preview-panel {
    max-height: none;
  }

  .video-list-panel {
    width: 100%;
    margin-right: 0;
    margin-bottom: 20px;
  }

  .vehicle-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .preview-content {
    flex-direction: column;
  }

  .video-details {
    width: 100%;
  }
}
</style>
