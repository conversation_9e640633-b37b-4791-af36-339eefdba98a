/* 直接匹配菜单DOM结构的样式修复 */

/* 匹配主菜单容器 */
.el-menu.el-menu--vertical.rr-sidebar-menu {
  background-color: #0A1F3D !important;
  border-right: none !important;
}

/* 匹配所有子菜单 - 增强特异性 */
.deep-blue-theme .el-menu.el-menu--inline,
.deep-blue-theme ul[role="menu"].el-menu.el-menu--inline,
.deep-blue-theme li.el-sub-menu ul.el-menu.el-menu--inline,
.el-menu--vertical .el-menu--popup,
.deep-blue-theme .el-menu.el-menu--vertical li.el-sub-menu ul.el-menu--inline {
  background-color: #071A33 !important;
}

/* 匹配父级菜单项标题 */
.el-sub-menu__title,
.deep-blue-theme div.el-sub-menu__title,
.deep-blue-theme li.el-sub-menu > div.el-sub-menu__title {
  background-color: #0A1F3D !important;
  color: white !important;
}

/* 匹配子菜单项 - 增强特异性 */
.el-menu-item,
.deep-blue-theme .el-menu.el-menu--inline .el-menu-item,
.deep-blue-theme .el-menu.el-menu--inline li.el-menu-item,
.deep-blue-theme ul[role="menu"].el-menu--inline > li.el-menu-item,
.deep-blue-theme li.el-sub-menu ul.el-menu--inline li.el-menu-item {
  background-color: #0A1F3D !important;
  color: white !important;
}

/* 匹配菜单中的文本和图标 */
.el-menu-item a,
.el-sub-menu__title span a,
.el-menu-item i svg,
.el-sub-menu__title i svg,
.el-sub-menu__icon-arrow svg,
.deep-blue-theme .el-menu-item a,
.deep-blue-theme .el-sub-menu__title span a {
  color: white !important;
  fill: white !important;
}

/* 匹配活动菜单项 */
.el-menu-item.is-active,
.deep-blue-theme .el-menu-item.is-active,
.deep-blue-theme .el-menu--inline .el-menu-item.is-active {
  background-color: #1D68C1 !important;
  color: white !important;
  border-left: 3px solid white !important;
}

/* 匹配展开的父菜单 */
.el-sub-menu.is-active > .el-sub-menu__title,
.deep-blue-theme .el-sub-menu.is-active > .el-sub-menu__title {
  color: white !important;
}

/* 针对SVG图标的颜色 */
.el-icon svg path,
.deep-blue-theme .el-icon svg path,
.deep-blue-theme .el-menu .el-icon svg path {
  fill: white !important;
}

/* 保证活动状态的图标也是白色 */
.el-menu-item.is-active i svg path,
.el-sub-menu.is-active > .el-sub-menu__title i svg path {
  fill: white !important;
}

/* 覆盖任何可能的内联样式 */
.el-menu--inline li.el-menu-item[style*="background-color"],
.el-menu--inline li.el-menu-item[style] {
  background-color: #071A33 !important;
}

/* 覆盖菜单弹出层的背景色 */
.el-menu--popup,
.el-menu--popup-container,
.el-menu--collapse .el-sub-menu .el-menu--popup,
.deep-blue-theme .el-menu--popup {
  background-color: #071A33 !important;
}

/* 直接使用!important覆盖内联样式 */
.deep-blue-theme ul[role="menu"].el-menu--inline {
  background-color: #071A33 !important;
}

/* 修复子菜单内联背景 */
.deep-blue-theme li.el-menu-item[style] {
  background-color: #071A33 !important;
} 