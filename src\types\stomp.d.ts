declare module '@stomp/stompjs' {
  export interface IFrame {
    command: string;
    headers: { [key: string]: string };
    body: string;
  }

  export interface IMessage {
    command: string;
    headers: { [key: string]: string };
    body: string;
    ack: (headers?: object) => void;
    nack: (headers?: object) => void;
  }

  export class Client {
    connected: boolean;
    
    constructor(config: {
      webSocketFactory?: () => WebSocket;
      brokerURL?: string;
      connectHeaders?: object;
      disconnectHeaders?: object;
      heartbeatIncoming?: number;
      heartbeatOutgoing?: number;
      reconnectDelay?: number;
      debug?: (msg: string) => void;
    });
    
    activate(): void;
    deactivate(): void;
    
    onConnect: (frame?: IFrame) => void;
    onDisconnect: (frame?: IFrame) => void;
    onStompError: (frame: IFrame) => void;
    onWebSocketClose: (event: CloseEvent) => void;
    onWebSocketError: (event: Event) => void;
    
    subscribe(destination: string, callback: (message: IMessage) => void, headers?: object): any;
    unsubscribe(id: string): void;
    
    begin(transactionId?: string): any;
    commit(transactionId: string): void;
    abort(transactionId: string): void;
    
    publish(parameters: {
      destination: string;
      headers?: object;
      body?: string;
      skipContentLengthHeader?: boolean;
    }): void;
  }
} 