declare module 'sockjs-client' {
  export default class SockJ<PERSON> implements WebSocket {
    constructor(url: string, _reserved?: any, options?: any);
    
    // WebSocket标准属性和方法
    readonly readyState: number;
    readonly bufferedAmount: number;
    readonly extensions: string;
    readonly protocol: string;
    readonly url: string;
    readonly binaryType: BinaryType;
    onclose: ((this: WebSocket, ev: CloseEvent) => any) | null;
    onerror: ((this: WebSocket, ev: Event) => any) | null;
    onmessage: ((this: WebSocket, ev: MessageEvent<any>) => any) | null;
    onopen: ((this: WebSocket, ev: Event) => any) | null;
    
    close(code?: number, reason?: string): void;
    send(data: string | ArrayBufferLike | Blob | ArrayBufferView): void;
    
    // SockJS特有的标准属性和方法
    OPEN: number;
    CLOSING: number;
    CONNECTING: number;
    CLOSED: number;
    
    addEventListener<K extends keyof WebSocketEventMap>(type: K, listener: (this: WebSocket, ev: WebSocketEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
    addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
    removeEventListener<K extends keyof WebSocketEventMap>(type: K, listener: (this: WebSocket, ev: WebSocketEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
    removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    dispatchEvent(event: Event): boolean;
  }
} 