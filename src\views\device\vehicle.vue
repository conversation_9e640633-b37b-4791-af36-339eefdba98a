<template>
  <div class="mod-device__vehicle">
    <el-form
      :inline="true"
      :model="state.dataForm"
      @keyup.enter="state.getDataList()"
      class="vehicle-form"
    >
      <el-form-item>
        <el-input
          v-model="state.dataForm.key"
          placeholder="车辆名称/车牌号/车辆型号/车辆制造商/注册单位"
          clearable
          style="width: 320px"
        ></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="state.getDataList()">查询</el-button>
      </el-form-item>
      <div class="form-right">
        <el-form-item>
          <el-button type="primary" @click="addOrUpdateHandle()">注册车辆</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="state.deleteHandle()">注销车辆</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      @selection-change="state.dataListSelectionChangeHandle"
      style="width: 100%"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column label="序号" header-align="center" align="center" width="60">
        <template v-slot="scope">
          {{ scope.$index + 1 + ((state.page || 1) - 1) * (state.limit || 10) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="deviceName"
        label="车辆名称"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="licensePlate"
        label="车牌号"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="model"
        label="型号"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="manufacturer"
        label="车辆制造商"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="registeredUnit"
        label="注册单位"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column label="注册时间" header-align="center" align="center">
        <template v-slot="scope">
          {{ formatDateTime(scope.row.createdAt) }}
        </template>
      </el-table-column>
      <el-table-column label="车辆访问密码" header-align="center" align="center">
        <template v-slot="scope">
          <div
            style="display: flex; align-items: center; justify-content: center; gap: 5px"
          >
            <span>{{ scope.row.showPassword ? scope.row.accessToken : "******" }}</span>
            <el-button
              type="primary"
              text
              size="small"
              circle
              @click="togglePasswordVisibility(scope.row)"
            >
              <el-icon>
                <component :is="scope.row.showPassword ? View : Hide" />
              </el-icon>
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        header-align="center"
        align="center"
        width="120"
      >
        <template v-slot="scope">
          <el-button
            type="primary"
            circle
            size="small"
            @click="addOrUpdateHandle(scope.row.id)"
          >
            <el-icon><Edit /></el-icon>
          </el-button>
          <el-button
            type="danger"
            circle
            size="small"
            @click="state.deleteHandle(scope.row.id)"
          >
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.page"
      :page-sizes="[5, 10, 20, 50, 100]"
      :page-size="state.limit"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="state.pageSizeChangeHandle"
      @current-change="state.pageCurrentChangeHandle"
    >
    </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"
      >确定</add-or-update
    >
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./vehicle-add-or-update.vue";
import { Edit, Delete, View, Hide } from "@element-plus/icons-vue";

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/device/vehicle/page",
  getDataListIsPage: true,
  exportURL: "/device/vehicle/export",
  deleteURL: "/device/vehicle",
  dataForm: {
    key: "",
  },
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

// 格式化日期时间为本地时间格式
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return "";

  try {
    // 创建日期对象 - 会自动转换为本地时区
    const date = new Date(dateTimeStr);

    // 格式化年月日
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");

    // 格式化时分秒
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const seconds = date.getSeconds().toString().padStart(2, "0");

    // 组合成最终格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error("日期格式化错误:", error);
    return dateTimeStr;
  }
};

interface VehicleRow {
  id?: number;
  deviceName?: string;
  licensePlate?: string;
  model?: string;
  manufacturer?: string;
  registeredUnit?: string;
  accessToken?: string;
  createdAt?: string;
  showPassword?: boolean;
  [key: string]: any;
}

// 切换密码显示/隐藏
const togglePasswordVisibility = (row: VehicleRow) => {
  row.showPassword = !row.showPassword;
};

// 在数据加载后给每行添加密码显示标记
const originalGetDataList = state.getDataList;
state.getDataList = async () => {
  await originalGetDataList();
  // 为每一行数据添加密码显示标志
  if (state.dataList) {
    state.dataList.forEach((row: VehicleRow) => {
      row.showPassword = false;
    });
  }
};

// 初始加载数据
state.getDataList();
</script>

<style scoped>
.vehicle-form {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.form-left {
  display: flex;
  flex-wrap: wrap;
}

.form-right {
  display: flex;
  justify-content: flex-end;
  margin-left: auto;
}
</style>
