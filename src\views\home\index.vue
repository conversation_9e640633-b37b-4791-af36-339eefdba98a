<template>
  <div class="patrol-dashboard">
    <!-- 顶部状态卡片区域 -->
    <status-cards :monitoring-data="monitoringData" />

    <el-input
      v-model="searchQuery"
      placeholder="搜索车辆编号/车牌号"
      class="search-bar"
    >
      <template #prefix>
        <el-icon>
          <Search />
        </el-icon>
      </template>
    </el-input>

    <!-- 中间主区域 -->
    <div class="content-layout">
      <!-- 左侧地图区域 -->
      <vehicle-map 
        ref="vehicleMapRef"
        :vehicles-data="vehiclesData" 
        @vehicle-click="handleVehicleClick"
      />

      <!-- 右侧监控区域 -->
      <monitor-area 
        :camera-options="cameraOptions"
        :selected-cameras="selectedCameras"
        :current-time="currentTime"
        @camera-change="handleCameraChange"
      />
    </div>

    <!-- 底部车辆信息区域 -->
    <div class="vehicle-info-area">
      <vehicle-carousel :vehicles-data="vehiclesData" />
      <task-list :tasks-data="tasksData" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount } from "vue";
import { Search } from "@element-plus/icons-vue";
import { ElNotification } from "element-plus";
import vehicleService from "@/services/vehicleService";

// 导入拆分的组件
import StatusCards from "./components/StatusCards.vue";
import VehicleMap from "./components/VehicleMap.vue";
import MonitorArea from "./components/MonitorArea.vue";
import VehicleCarousel from "./components/VehicleCarousel.vue";
import TaskList from "./components/TaskList.vue";

// 当前时间
const currentTime = ref<string>("00:00:00");
const vehicleMapRef = ref(null);

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");
  currentTime.value = `${hours}:${minutes}:${seconds}`;
};

// 每秒更新时间
const timeInterval = setInterval(updateCurrentTime, 1000);
updateCurrentTime(); // 初始化时间

// 搜索查询
const searchQuery = ref('');

// 摄像头选项
const cameraOptions = [
  { value: 'camera1', label: '前方路口' },
  { value: 'camera2', label: '东区道路' },
  { value: 'camera3', label: '西区监控' },
  { value: 'camera4', label: '南区通道' },
  { value: 'camera5', label: '北区大门' }
];

// 选中的摄像头
const selectedCameras = reactive({
  camera1: 'camera1',
  camera2: 'camera2',
  camera3: 'camera3'
});

// 监控数据
const monitoringData = reactive({
  onlineVehicles: 0,
  totalVehicles: 8,
  completionRate: 0,
  todayDistance: 125,
  warningMessage: "车辆A-2025电量不足20%，请及时安排充电",
  taskCompletionRate: 68
});

// 模拟车辆数据
const vehiclesData = reactive([
  {
    id: "A-2025",
    status: "online",
    battery: 75,
    speed: 32,
    location: "武侯区人民南路"
  },
  {
    id: "B-2026",
    status: "online",
    battery: 45,
    speed: 0,
    location: "锦江区春熙路"
  },
  {
    id: "C-2027",
    status: "charging",
    battery: 15,
    speed: 0,
    location: "青羊区天府广场"
  },
  {
    id: "D-2028",
    status: "offline",
    battery: 0,
    speed: 0,
    location: "金牛区茶店子"
  },
  {
    id: "E-2029",
    status: "online",
    battery: 90,
    speed: 28,
    location: "成华区建设路"
  },
  {
    id: "F-2030",
    status: "online",
    battery: 65,
    speed: 45,
    location: "双流区双流国际机场"
  },
  {
    id: "G-2031",
    status: "online",
    battery: 60,
    speed: 22,
    location: "高新区天府软件园"
  },
  {
    id: "H-2032",
    status: "charging",
    battery: 25,
    speed: 0,
    location: "龙泉驿区十陵镇"
  }
]);

// 任务列表数据
const tasksData = reactive([
  {
    time: "08:30:25",
    location: "武侯区人民南路",
    type: "例行巡逻",
    status: "completed",
    handler: "王小明"
  },
  {
    time: "09:45:12",
    location: "锦江区春熙路",
    type: "设备维护",
    status: "processing",
    handler: "李小红"
  },
  {
    time: "10:15:36",
    location: "青羊区天府广场",
    type: "安全检查",
    status: "processing",
    handler: "张小龙"
  },
  {
    time: "11:30:20",
    location: "高新区天府三街",
    type: "例行巡逻",
    status: "completed",
    handler: "刘小华"
  },
  {
    time: "13:45:10",
    location: "金牛区万达广场",
    type: "应急响应",
    status: "processing",
    handler: "赵小勇"
  },
  {
    time: "15:20:45",
    location: "成华区建设路",
    type: "安全检查",
    status: "completed",
    handler: "孙小丽"
  }
]);

// 摄像头变更处理
const handleCameraChange = (cameraId: string, value: string) => {
  selectedCameras[cameraId] = value;
};

// 处理车辆点击
const handleVehicleClick = (vehicle: any, marker: any) => {
  // 可以在这里处理车辆点击事件
  console.log('点击了车辆:', vehicle);
};

// 更新车辆卡片信息
const updateVehicleCard = (vehicleStatus: any) => {
  const { deviceName, licensePlate, batteryLevel, currentSpeed, chargingStatus, lon, lat } = vehicleStatus;
  
  // 获取地理位置描述（实际项目中可以使用逆地理编码获取）
  const location = "实时定位中";
  
  // 查找并更新车辆数据
  const vehicleIndex = vehiclesData.findIndex(v => v.id === deviceName || v.id === licensePlate);
  
  if (vehicleIndex !== -1) {
    // 更新现有车辆数据
    vehiclesData[vehicleIndex].battery = batteryLevel;
    vehiclesData[vehicleIndex].speed = currentSpeed;
    vehiclesData[vehicleIndex].status = chargingStatus ? "charging" : "online";
    vehiclesData[vehicleIndex].location = location;
  } else {
    // 添加新车辆
    vehiclesData.push({
      id: deviceName,
      status: chargingStatus ? "charging" : "online",
      battery: batteryLevel,
      speed: currentSpeed,
      location: location
    });
  }
};

// 处理车辆状态更新
const handleVehicleUpdate = (vehicleStatus: any) => {
  // 将更新传递给地图组件
  if (vehicleMapRef.value) {
    vehicleMapRef.value.updateVehicleOnMap(vehicleStatus);
  }
  
  // 更新车辆信息卡片
  updateVehicleCard(vehicleStatus);
  
  // 更新监控数据统计
  updateMonitoringStats();
};

// 更新监控统计数据
const updateMonitoringStats = () => {
  // 更新在线车辆数
  monitoringData.onlineVehicles = vehiclesData.filter(v => v.status !== "offline").length;
  
  // 更新完成率（根据在线车辆与总车辆比例）
  monitoringData.completionRate = Math.round((monitoringData.onlineVehicles / monitoringData.totalVehicles) * 100);
};

// 模拟数据变化
const simulateDataChanges = () => {
  // 随机更新数据
  const dataInterval = setInterval(() => {
    // 更新完成率
    monitoringData.completionRate = Math.min(100, monitoringData.completionRate + (Math.random() > 0.7 ? 1 : 0));

    // 更新里程数
    monitoringData.todayDistance += Math.floor(Math.random() * 5);

    // 更新任务完成率
    monitoringData.taskCompletionRate = Math.min(100, parseFloat((monitoringData.taskCompletionRate + Math.random() * 0.1).toFixed(1)));

    // 随机增减在线车辆
    if (Math.random() > 0.8) {
      monitoringData.onlineVehicles = Math.min(
        monitoringData.totalVehicles,
        Math.max(monitoringData.onlineVehicles + (Math.random() > 0.5 ? 1 : -1), 0)
      );
    }
  }, 3000); // 每3秒更新一次

  // 在组件卸载前清除定时器
  onBeforeUnmount(() => {
    clearInterval(dataInterval);
  });
};

// 模拟车辆数据变化
const simulateVehicleDataChanges = () => {
  const vehicleInterval = setInterval(() => {
    vehiclesData.forEach(vehicle => {
      if (vehicle.status === "online") {
        // 随机更新速度
        vehicle.speed = Math.max(0, vehicle.speed + (Math.random() > 0.5 ? 1 : -1) * Math.floor(Math.random() * 3));

        // 随机消耗电量
        if (Math.random() > 0.7) {
          vehicle.battery = Math.max(0, vehicle.battery - 1);

          // 如果电量过低，状态改为充电
          if (vehicle.battery < 10 && Math.random() > 0.7) {
            vehicle.status = "charging";
            vehicle.speed = 0;
          }
        }
      } else if (vehicle.status === "charging") {
        // 充电中，增加电量
        vehicle.battery = Math.min(100, vehicle.battery + 1);

        // 如果充满电，状态改为在线
        if (vehicle.battery > 90 && Math.random() > 0.7) {
          vehicle.status = "online";
        }
      } else if (vehicle.status === "offline" && Math.random() > 0.9) {
        // 有低概率从离线恢复在线
        vehicle.status = "online";
        vehicle.battery = 50 + Math.floor(Math.random() * 50);
      }
    });
  }, 5000); // 每5秒更新一次

  // 在组件卸载前清除定时器
  onBeforeUnmount(() => {
    clearInterval(vehicleInterval);
  });
};

// 组件挂载完成后初始化
onMounted(() => {
  simulateDataChanges();
  simulateVehicleDataChanges();
  
  // 设置车辆更新回调
  vehicleService.onVehicleUpdate(handleVehicleUpdate);
  
  // 连接WebSocket
  vehicleService.connect().catch(error => {
    console.error("连接车辆服务失败:", error);
    
    // 显示错误通知，3秒后自动消失
    ElNotification({
      title: '连接失败',
      message: '无法连接到车辆实时数据服务，将使用模拟数据',
      type: 'warning',
      duration: 3000 // 3秒后自动消失
    });
  });
});

// 组件卸载时清除定时器和连接
onBeforeUnmount(() => {
  // 清除时间更新定时器
  clearInterval(timeInterval);
  
  // 断开WebSocket连接
  vehicleService.disconnect();
});
</script>

<style lang="less" scoped>
.patrol-dashboard {
  width: 100%;
  height: 100vh;
  background-color: #001529;
  color: white;
  font-family: 'Microsoft YaHei', sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: radial-gradient(ellipse at center, #0a2e50 0%, #001529 100%);
  position: relative;
}

// 搜索栏
.search-bar {
  margin: 0 15px 15px;
  width: calc(100% - 30px);
  max-width: 300px;
  
  :deep(.el-input__inner) {
    background: rgba(0, 21, 41, 0.6);
    border: 1px solid rgba(24, 144, 255, 0.3);
    color: white;
    
    &::placeholder {
      color: rgba(255, 255, 255, 0.4);
    }
  }
  
  :deep(.el-input__prefix) {
    color: rgba(24, 144, 255, 0.8);
  }
}

// 内容布局
.content-layout {
  display: flex;
  padding: 0 15px;
  margin-bottom: 15px;
  flex: 1;
  min-height: 0;
}

// 车辆信息区域
.vehicle-info-area {
  padding: 0 15px 15px;
  margin-right: 295px; /* 为右侧监控区域留出空间 */
  display: flex;
  flex-direction: column;
  height: auto; /* 自适应高度 */
  flex-shrink: 0; /* 防止高度被压缩 */
}

// 响应式处理
@media screen and (max-width: 1200px) {
  .content-layout {
    flex-direction: column;
  }

  .vehicle-info-area {
    margin-right: 0;
  }
}
</style> 