<script lang="ts">
import "@/assets/css/app.less";
import "@/assets/theme/index.less";
import "@/assets/theme/mobile.less";
// 引入自定义深蓝色主题
import "@/assets/css/deep-blue-theme.less";
import FullscreenLayout from "@/layout/fullscreen-layout.vue";
import Layout from "@/layout/index.vue";
import { ElConfigProvider } from "element-plus";
import { defineComponent, onMounted, reactive, watch } from "vue";
import { useRoute } from "vue-router";
import { useAppStore } from "@/store";
import app from "./constants/app";
import { EPageLayoutEnum, EThemeColor, EThemeSetting, EMitt } from "./constants/enum";
import { IObject } from "./types/interface";
import { getThemeConfigCache, setThemeColor, updateTheme, setThemeConfigToCache } from "./utils/theme";
import emits from "./utils/emits";

export default defineComponent({
  name: "App",
  components: { Layout, FullscreenLayout, ElConfigProvider },
  setup() {
    const store = useAppStore();
    const route = useRoute();
    const state = reactive({
      layout: location.href.includes("pop=true") ? EPageLayoutEnum.fullscreen : EPageLayoutEnum.page
    });
    onMounted(() => {
      // 应用深蓝色主题
      document.documentElement.classList.add('deep-blue-theme');
      document.body.classList.add('deep-blue-theme');
      
      // 设置Element Plus默认主题色 #409EFF
      const elementDefaultThemeColor = "#409EFF";
      setThemeConfigToCache(EThemeSetting.ThemeColor, elementDefaultThemeColor);
      setThemeColor(EThemeColor.ThemeColor, elementDefaultThemeColor);
      updateTheme(elementDefaultThemeColor);

      // 关闭标签页功能
      setThemeConfigToCache(EThemeSetting.OpenTabsPage, false);
      emits.emit(EMitt.OnSetThemeTabsPage, false);
      
      // 设置侧边栏主题为dark
      setThemeConfigToCache(EThemeSetting.Sidebar, "dark");
      emits.emit(EMitt.OnSetTheme, [EThemeSetting.Sidebar, "dark"]);
    });
    watch(
      () => [route.path, route.query, route.fullPath],
      ([path, query, fullPath]) => {
        store.updateState({ activeTabName: fullPath });
        state.layout = app.fullscreenPages.includes(path as string) || (query as IObject)["pop"] ? EPageLayoutEnum.fullscreen : EPageLayoutEnum.page;
      }
    );
    return {
      store,
      state,
      pageTag: EPageLayoutEnum.page
    };
  }
});
</script>
<template>
  <el-config-provider>
    <div v-if="!store.state.appIsRender" v-loading="true" :element-loading-fullscreen="true" :element-loading-lock="true" style="width: 100vw; height: 100vh; position: absolute; top: 0; left: 0; z-index: 99999; background: #0A1F3D"></div>
    <template v-if="store.state.appIsReady">
      <layout v-if="state.layout === pageTag"> </layout>
      <fullscreen-layout v-else></fullscreen-layout>
    </template>
  </el-config-provider>
</template>
