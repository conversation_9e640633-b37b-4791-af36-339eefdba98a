<script lang="ts">
import SvgIcon from "@/components/base/svg-icon";
import ThemeToggle from "./theme-toggle.vue";
import baseService from "@/service/baseService";
import { useFullscreen } from "@vueuse/core";
import { defineComponent } from "vue";
import { useRouter } from "vue-router";
import { useAppStore } from "@/store";
import userLogo from "@/assets/images/user.png";
import "@/assets/css/header.less";
import { ElMessageBox } from "element-plus";

interface IExpand {
  userName?: string;
}

/**
 * 顶部右侧扩展区域
 */
export default defineComponent({
  name: "Expand",
  components: { SvgIcon, ThemeToggle },
  props: {
    userName: String
  },
  setup(props: IExpand) {
    const router = useRouter();
    const store = useAppStore();
    const { isFullscreen, toggle } = useFullscreen();

    const onClickUserMenus = (path: string) => {
      if (path === "/login") {
        ElMessageBox.confirm("确定进行[退出]操作?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            baseService.post("/logout").finally(() => {
              router.push(path);
            });
          })
          .catch(() => {
            //
          });
      } else {
        router.push(path);
      }
    };
    return {
      props,
      store,
      isFullscreen,
      userLogo,
      onClickUserMenus,
      toggle
    };
  }
});
</script>
<template>
  <div class="rr-header-right-items">
    <!-- <div class="header-icon-item">
      <a href="https://www.renren.io/community" target="_blank">
        <svg-icon name="icon-earth"></svg-icon>
      </a>
    </div>
    <div class="header-icon-item">
      <a href="https://gitee.com/renrenio/renren-security" target="_blank">
        <svg-icon name="icon-gitee"></svg-icon>
      </a>
    </div> -->
    <div class="header-icon-item hidden-xs-only" @click="toggle">
      <span>
        <svg-icon :name="isFullscreen ? 'tuichuquanping' : 'fullscreen2'"></svg-icon>
      </span>
    </div>
    <div class="header-icon-item hidden-xs-only">
      <theme-toggle></theme-toggle>
    </div>
    <div class="user-profile">
      <img :src="userLogo" :alt="props.userName" class="user-avatar" />
      <el-dropdown @command="onClickUserMenus">
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item icon="lock" command="/user/password"> 修改密码 </el-dropdown-item>
            <el-dropdown-item icon="switch-button" divided command="/login"> 退出登录 </el-dropdown-item>
          </el-dropdown-menu>
        </template>
        <span class="el-dropdown-link">
          {{ props.userName }}
          <el-icon class="el-icon--right" style="font-size: 14px"><arrow-down /></el-icon>
        </span>
      </el-dropdown>
    </div>
  </div>
</template>

<style lang="less" scoped>
.rr-header-right-items {
  display: flex;
  padding: 0 8px 0 0;
  align-items: center;
  height: 50px;

  .header-icon-item {
    padding: 0 12px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    
    a {
      display: flex;
      align-items: center;
      height: 100%;
    }
  }

  .user-profile {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    height: 50px;
  }

  .user-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .el-dropdown-link {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}
</style>
