<template>
  <div class="mod-task__patrolroute">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()" class="patrolroute-form">
      <el-form-item>
        <el-input v-model="state.dataForm.key" placeholder="车辆路线名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="state.getDataList()">查询</el-button>
      </el-form-item>
      <div class="form-right">
        <el-form-item>
          <el-button type="success" @click="viewAllRoutes()">查看所有作业路线</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="addOrUpdateHandle()">新增作业路线</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="danger" @click="state.deleteHandle()">删除作业路线</el-button>
        </el-form-item>
      </div>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
              <el-table-column prop="id" label="编号" header-align="center" align="center" width="60"></el-table-column>
              <el-table-column prop="routeName" label="作业路线名称" header-align="center" align="center"></el-table-column>
              <el-table-column prop="routeDes" label="作业路线描述" header-align="center" align="center"></el-table-column>
              <el-table-column label="作业路线" header-align="center" align="center">
                <template v-slot="scope">
                  <div class="route-preview">
                    <!-- <div class="route-preview-mini" :id="`route-preview-${scope.row.id}`" ref="routePreview"></div> -->
                    <el-button class="iconfont icon-a-02_luxian large-icon" type="primary" link @click="showRouteDetail(scope.row.route, scope.row.routeName)">
                      预览路线
                    </el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="creatTime" label="创建时间" header-align="center" align="center">
                <template v-slot="scope">
                  {{ formatDateTime(scope.row.creatTime) }}
                </template>
              </el-table-column>
              <el-table-column prop="updateTime" label="更新时间" header-align="center" align="center">
                <template v-slot="scope">
                  {{ formatDateTime(scope.row.updateTime) }}
                </template>
              </el-table-column>
            <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
          <el-button type="primary" circle size="small" @click="addOrUpdateHandle(scope.row.id)">
            <el-icon><Edit /></el-icon>
          </el-button>
          <el-button type="danger" circle size="small" @click="state.deleteHandle(scope.row.id)">
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
    
    <!-- 路线详情弹窗 -->
    <el-dialog
      v-model="routeDialogVisible"
      :title="routeDialogTitle"
      width="70%"
      destroy-on-close
      :close-on-click-modal="false"
    >
      <div class="route-map-container">
        <div id="route-detail-map" class="route-detail-map"></div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="routeDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 所有路线地图弹窗 -->
    <el-dialog
      v-model="allRoutesDialogVisible"
      title="所有作业路线地图"
      width="90%"
      destroy-on-close
      :close-on-click-modal="false"
      :before-close="handleCloseAllRoutesDialog"
      top="5vh"
    >
      <div class="route-map-container">
        <div id="all-routes-map" class="all-routes-map"></div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="allRoutesDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, onMounted, nextTick, onUnmounted } from "vue";
import AddOrUpdate from "./patrolroute-add-or-update.vue";
import { ElMessage } from "element-plus";
import { ZoomIn, Edit, Delete } from "@element-plus/icons-vue";

// 声明全局变量用于保存地图实例
declare global {
  interface Window {
    AMap?: any;
    _AMapSecurityConfig?: any;
  }
}

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/patrolroute/page",
  getDataListIsPage: true,
  exportURL: "/task/patrolroute/export",
  deleteURL: "/task/patrolroute",
  dataForm: {
    key: ''
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });
const routeDialogVisible = ref(false);
const routeDialogTitle = ref('路线详情');
const mapLoaded = ref(false);
const routePreview = ref([]);
const currentRouteGeoJSON = ref('');

// 所有路线地图对话框控制
const allRoutesDialogVisible = ref(false);
const allRoutesMap = ref(null);

// 加载高德地图API
const loadAMap = () => {
  if(window.AMap) {
    mapLoaded.value = true;
    return Promise.resolve();
  }
  
  return new Promise<void>((resolve, reject) => {
    // 配置高德地图安全密钥（需要替换为您的密钥）
    window._AMapSecurityConfig = {
      securityJsCode: "a9676a96467adef5269a629f780bdb07",
    };
    
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.async = true;
    script.src = 'https://webapi.amap.com/maps?v=2.0&key=eea9447f9cd0ad60cd7a7b612974f3fa&plugin=AMap.PolyEditor,AMap.ToolBar,AMap.Scale';
    script.onload = () => {
      mapLoaded.value = true;
      resolve();
    };
    script.onerror = () => {
      reject(new Error('高德地图加载失败'));
    };
    document.head.appendChild(script);
  });
};

// 解析GeoJSON字符串
const parseGeoJSON = (geoJsonString) => {
  try {
    // 处理可能的多行内容，去除换行符
    const cleanedString = geoJsonString.replace(/\n/g, '');
    return JSON.parse(cleanedString);
  } catch (error) {
    console.error('GeoJSON解析错误:', error);
    ElMessage.error('路线数据格式错误');
    return null;
  }
};

// 显示路线详情
const showRouteDetail = async (geoJsonString, title) => {
  routeDialogTitle.value = title ? `路线详情: ${title}` : '路线详情';
  routeDialogVisible.value = true;
  currentRouteGeoJSON.value = geoJsonString;
  
  // 等待DOM更新
  await nextTick();
  
  // 确保地图API已加载
  if (!mapLoaded.value) {
    try {
      await loadAMap();
    } catch (error) {
      ElMessage.error('地图加载失败，请检查网络连接');
      return;
    }
  }
  
  // 初始化地图
  const map = new window.AMap.Map('route-detail-map', {
    zoom: 13,
    viewMode: '3D'
  });
  
  // 解析GeoJSON
  const geoJson = parseGeoJSON(geoJsonString);
  if (!geoJson) return;
  
  // 绘制路线
  if (geoJson.geometry && geoJson.geometry.type === 'LineString' && geoJson.geometry.coordinates) {
    const path = geoJson.geometry.coordinates.map(coord => new window.AMap.LngLat(coord[0], coord[1]));
    
    // 创建折线
    const polyline = new window.AMap.Polyline({
      path: path,
      strokeColor: '#3498DB',
      strokeWeight: 6,
      strokeOpacity: 0.8,
      showDir: true,
      dirColor: '#5DADE2'
    });
    
    // 将折线添加到地图
    map.add(polyline);
    
    // 调整视图以显示整条路线
    map.setFitView([polyline]);
    
    // 添加起点和终点标记
    if (path.length > 0) {
      // 起点标记
      const startMarker = new window.AMap.Marker({
        position: path[0],
        icon: new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: 'https://webapi.amap.com/theme/v1.3/markers/n/start.png',
          imageSize: new window.AMap.Size(25, 34)
        }),
        title: '起点',
        offset: new window.AMap.Pixel(-13, -34)
      });
      
      // 终点标记
      const endMarker = new window.AMap.Marker({
        position: path[path.length - 1],
        icon: new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: 'https://webapi.amap.com/theme/v1.3/markers/n/end.png',
          imageSize: new window.AMap.Size(25, 34)
        }),
        title: '终点',
        offset: new window.AMap.Pixel(-13, -34)
      });
      
      map.add([startMarker, endMarker]);
    }
  } else {
    ElMessage.warning('不支持的路线格式');
  }
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

// 优化时间显示
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return '';
  
  try {
    // 创建日期对象 - 会自动转换为本地时区
    const date = new Date(dateTimeStr);
    
    // 格式化年月日
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // 格式化时分秒
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    // 组合成最终格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateTimeStr;
  }
};

// 查看所有作业路线
const viewAllRoutes = async () => {
  // 显示对话框
  allRoutesDialogVisible.value = true;
  
  // 等待DOM更新完成
  await nextTick();
  
  // 确保地图API已加载
  if (!mapLoaded.value) {
    try {
      await loadAMap();
    } catch (error) {
      ElMessage.error('地图加载失败，请检查网络连接');
      return;
    }
  }
  
  // 初始化地图
  const map = new window.AMap.Map('all-routes-map', {
    zoom: 12,
    viewMode: '3D',
  });
  
  // 保存地图实例以便后续清理
  allRoutesMap.value = map;
  
  // 添加控件
  map.plugin(['AMap.ToolBar', 'AMap.Scale'], function(){
    // 添加工具条控件
    const toolbar = new window.AMap.ToolBar({ position: 'RB' });
    // 添加比例尺控件
    const scale = new window.AMap.Scale();
    
    map.addControl(toolbar);
    map.addControl(scale);
  });
  
  // 检查是否有路线数据
  if (!state.dataList || state.dataList.length === 0) {
    ElMessage.warning('没有可显示的路线数据');
    return;
  }
  
  // 创建图例
  createLegend(map);
  
  // 用于存储所有图形元素，以便调整视图
  const allElements = [];
  
  // 预定义不同路线的颜色
  const colors = [
    '#3498DB', '#E74C3C', '#2ECC71', '#F39C12', '#9B59B6', 
    '#1ABC9C', '#D35400', '#27AE60', '#8E44AD', '#F1C40F'
  ];
  
  // 遍历路线数据并绘制到地图上
  state.dataList.forEach((item, index) => {
    try {
      if (!item.route) return;
      
      // 解析GeoJSON
      const geoJson = parseGeoJSON(item.route);
      if (!geoJson || !geoJson.geometry || !geoJson.geometry.coordinates || geoJson.geometry.coordinates.length < 2) {
        return;
      }
      
      // 选择颜色
      const color = colors[index % colors.length];
      
      // 获取路线坐标
      const coordinates = geoJson.geometry.coordinates;
      const path = coordinates.map(coord => new window.AMap.LngLat(coord[0], coord[1]));
      
      // 创建折线
      const polyline = new window.AMap.Polyline({
        path: path,
        strokeColor: color,
        strokeWeight: 6,
        strokeOpacity: 0.8,
        showDir: true,
        dirColor: color,
        extData: { routeName: item.routeName } // 存储路线名称
      });
      
      // 添加到地图
      map.add(polyline);
      allElements.push(polyline);
      
      // 获取路线中间点作为标签位置
      const middlePointIndex = Math.floor(path.length / 2);
      const labelPosition = path[middlePointIndex];
      
      // 创建文本标注
      const textMarker = new window.AMap.Marker({
        position: labelPosition,
        content: `<div class="route-text-label" style="background-color:${color};">${item.routeName || '未命名路线'}</div>`,
        anchor: 'center',
        offset: new window.AMap.Pixel(0, 0),
        zIndex: 100
      });
      
      // 添加标注到地图
      map.add(textMarker);
      allElements.push(textMarker);
      
      // 添加起点和终点标记
      const startMarker = new window.AMap.Marker({
        position: path[0],
        icon: new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: 'https://webapi.amap.com/theme/v1.3/markers/n/start.png',
          imageSize: new window.AMap.Size(25, 34)
        }),
        title: `起点：${item.routeName || '未命名路线'}`,
        offset: new window.AMap.Pixel(-13, -34)
      });
      
      const endMarker = new window.AMap.Marker({
        position: path[path.length - 1],
        icon: new window.AMap.Icon({
          size: new window.AMap.Size(25, 34),
          image: 'https://webapi.amap.com/theme/v1.3/markers/n/end.png',
          imageSize: new window.AMap.Size(25, 34)
        }),
        title: `终点：${item.routeName || '未命名路线'}`,
        offset: new window.AMap.Pixel(-13, -34)
      });
      
      map.add([startMarker, endMarker]);
      allElements.push(startMarker, endMarker);
      
    } catch (error) {
      console.error(`显示路线 ${item.routeName || '未命名路线'} 失败:`, error);
    }
  });
  
  // 调整视图以显示所有路线
  if (allElements.length > 0) {
    map.setFitView(allElements, false, [80, 80, 80, 80]);
  }
};

// 创建地图图例
const createLegend = (map) => {
  // 创建图例容器
  const legend = document.createElement('div');
  legend.className = 'map-legend';
  legend.innerHTML = `
    <div class="map-legend-title">路线标识说明</div>
    <div class="map-legend-item">
      <img src="https://webapi.amap.com/theme/v1.3/markers/n/start.png" width="20" height="28">
      <span>起点</span>
    </div>
    <div class="map-legend-item">
      <img src="https://webapi.amap.com/theme/v1.3/markers/n/end.png" width="20" height="28">
      <span>终点</span>
    </div>
    <div class="map-legend-item">
      <div class="map-legend-line"></div>
      <span>路线</span>
    </div>
    <div class="map-legend-item">
      <div class="map-legend-label">路线名称</div>
      <span>路线标签</span>
    </div>
  `;
  
  // 使用正确的方式创建自定义控件
  const CustomControl = function() {
    this.dom = legend;
    this.position = 'LB'; // 左下角
  };
  
  // 继承自定义控件的方法
  CustomControl.prototype.addTo = function(map) {
    this.map = map;
    this.map.getContainer().appendChild(this.dom);
    return this;
  };
  
  CustomControl.prototype.removeFrom = function(map) {
    this.map = map;
    this.map.getContainer().removeChild(this.dom);
    return this;
  };
  
  // 添加到地图
  const customControl = new CustomControl();
  customControl.addTo(map);
};

// 处理关闭所有路线地图对话框
const handleCloseAllRoutesDialog = () => {
  // 清理地图实例
  if (allRoutesMap.value) {
    allRoutesMap.value.destroy();
    allRoutesMap.value = null;
  }
  allRoutesDialogVisible.value = false;
};

// 组件挂载时加载地图API
onMounted(() => {
  loadAMap();
});

// 清理资源
onUnmounted(() => {
  // 清理地图实例
  if (allRoutesMap.value) {
    allRoutesMap.value.destroy();
    allRoutesMap.value = null;
  }
});
</script>

<style scoped>
.patrolroute-form {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.form-left {
  display: flex;
  flex-wrap: wrap;
}

.form-right {
  display: flex;
  justify-content: flex-end;
  margin-left: auto;
}

.route-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 放大图标 */
.large-icon {
  font-size: 18px !important;
}

.route-preview-mini {
  width: 80px;
  height: 40px;
  margin: 0 auto 5px;
  background-color: #f5f7fa;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.route-detail-map {
  width: 100%;
  height: 500px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.all-routes-map {
  width: 100%;
  height: 70vh;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.route-map-container {
  padding: 10px;
  background: #f5f7fa;
  border-radius: 4px;
}

/* 地图图例样式 */
.map-legend {
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 5px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  max-width: 200px;
  font-size: 12px;
}

.map-legend-title {
  font-weight: bold;
  margin-bottom: 8px;
  text-align: center;
  color: #333;
}

.map-legend-item {
  display: flex;
  align-items: center;
  margin: 5px 0;
}

.map-legend-item img {
  margin-right: 8px;
}

.map-legend-item span {
  color: #606266;
}

.map-legend-line {
  width: 20px;
  height: 3px;
  background-color: #3498DB;
  margin-right: 8px;
  border-radius: 2px;
}

.map-legend-label {
  background-color: #3498DB;
  color: white;
  padding: 2px 4px;
  border-radius: 2px;
  font-size: 10px;
  margin-right: 8px;
}

/* 路线文本标签样式 */
:deep(.route-text-label) {
  color: white;
  padding: 4px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  user-select: none;
}
</style>
