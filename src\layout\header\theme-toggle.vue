<script lang="ts">
import SvgIcon from "@/components/base/svg-icon";
import { defineComponent, ref } from "vue";

/**
 * 主题切换组件
 */
export default defineComponent({
  name: "ThemeToggle",
  components: { SvgIcon },
  setup() {
    const isDeepBlueTheme = ref(document.documentElement.classList.contains('deep-blue-theme'));

    const toggleTheme = () => {
      isDeepBlueTheme.value = !isDeepBlueTheme.value;
      
      if (isDeepBlueTheme.value) {
        // 启用深蓝色主题
        document.documentElement.classList.add('deep-blue-theme');
        document.body.classList.add('deep-blue-theme');
        document.querySelector('.rr')?.classList.add('deep-blue-theme');
      } else {
        // 禁用深蓝色主题
        document.documentElement.classList.remove('deep-blue-theme');
        document.body.classList.remove('deep-blue-theme');
        document.querySelector('.rr')?.classList.remove('deep-blue-theme');
      }
    };

    return {
      isDeepBlueTheme,
      toggleTheme
    };
  }
});
</script>
<template>
  <el-tooltip :content="isDeepBlueTheme ? '切换到浅色模式' : '切换到深色模式'">
    <span class="theme-toggle" :class="{ 'is-dark': isDeepBlueTheme }" @click="toggleTheme">
      <el-icon v-if="isDeepBlueTheme">
        <svg-icon name="sunny" />
      </el-icon>
      <el-icon v-else>
        <svg-icon name="moon" />
      </el-icon>
    </span>
  </el-tooltip>
</template>

<style scoped>
.theme-toggle {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  transition: all 0.3s;
  position: relative;
  width: 22px;
}

.theme-toggle.is-dark {
  color: #fff;
}

.theme-toggle:after {
  content: '';
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: transparent;
}

.theme-toggle.is-dark:after {
  background-color: #409EFF;
}

:deep(.el-tooltip__trigger) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.theme-toggle:hover {
  transform: scale(1.1);
}
</style> 