<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '注册车辆' : '修改车辆信息'" :close-on-click-modal="false"
    :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()"
      label-width="120px">
      <el-form-item label="车辆名称" prop="deviceName">
        <el-input v-model="dataForm.deviceName" placeholder="请输入车辆名称"></el-input>
      </el-form-item>
      <el-form-item label="车牌号" prop="licensePlate">
        <el-input v-model="dataForm.licensePlate" placeholder="请输入车牌号"></el-input>
      </el-form-item>
      <el-form-item label="型号" prop="model">
        <el-input v-model="dataForm.model" placeholder="请输入车辆型号"></el-input>
      </el-form-item>
      <el-form-item label=" 车辆制造商" prop="manufacturer">
        <el-input v-model="dataForm.manufacturer" placeholder="请输入车辆制造商"></el-input>
      </el-form-item>
      <el-form-item label="注册单位" prop="registeredUnit">
        <el-input v-model="dataForm.registeredUnit" placeholder="请输入车辆注册单位"></el-input>
      </el-form-item>
      <el-form-item label="车辆访问密码" prop="accessToken">
        <el-input v-model="dataForm.accessToken" placeholder="请设置车辆访问密码"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',deviceName:'', licensePlate: '', model: '', manufacturer: '', createdAt: '', registeredUnit: '', accessToken: ''
});

const rules = ref({
  deviceName: [
    { required: true, message: '车辆名称不能为空', trigger: 'blur' }
  ],
  licensePlate: [
    { required: true, message: '车牌号不能为空', trigger: 'blur' }
  ],
  model: [
    { required: true, message: '车辆型号不能为空', trigger: 'blur' }
  ], 
  manufacturer: [
    { required: true, message: '车辆制造商不能为空', trigger: 'blur' }
  ],
  registeredUnit:[
    { required: true, message: '车辆所属单位不能为空', trigger: 'blur' }
  ],
  accessToken: [
    { required: true, message: '车辆访问密码不能为空', trigger: 'blur' },
    { min: 6, max: 12, message: '车辆访问密码长度在6-12个字符之间', trigger: 'blur' }
  ]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/device/vehicle/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/device/vehicle", dataForm).then((res) => {
      ElMessage.success({
        message: '注册或修改车辆信息成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
