/* 强制修复登录表单中图标与文本间距问题 */

/* 调整输入框内部结构布局 */
.rr-login-right .el-input__wrapper {
  padding: 0 11px !important;
  display: flex !important;
  align-items: center !important;
}

/* 调整前缀图标区域 */
.rr-login-right .el-input__prefix {
  margin-right: 5px !important;
  padding: 0 !important;
}

/* 调整图标大小和间距 */
.rr-login-right .el-input__prefix .el-icon,
.rr-login-right .el-input__prefix-inner .el-icon {
  font-size: 14px !important;
  margin-right: 0 !important;
}

/* 确保图标颜色正确 */
.rr-login-right .el-input__prefix .el-icon,
.rr-login-right .el-input__prefix i,
.rr-login-right .el-input__prefix-inner .el-icon,
.rr-login-right .el-input__prefix span i,
.rr-login-right [prefix-icon] .el-input__prefix .el-icon {
  color: #333 !important;
}

/* 调整SVG图标 */
.rr-login-right .el-input__prefix svg,
.rr-login-right .el-input__prefix i svg,
.rr-login-right .el-input__prefix .el-icon svg {
  width: 14px !important;
  height: 14px !important;
  fill: #333 !important;
}

/* 调整输入框内部文本区域 */
.rr-login-right .el-input__inner {
  padding-left: 0 !important;
}

/* 确保特定输入框样式 */
.rr-login-right [prefix-icon="user"] .el-input__wrapper,
.rr-login-right [prefix-icon="lock"] .el-input__wrapper,
.rr-login-right [prefix-icon="first-aid-kit"] .el-input__wrapper {
  padding-left: 8px !important;
}

/* 确保所有文本为黑色 */
.rr-login-right .el-input__inner,
.rr-login-right .el-input__inner::placeholder {
  color: #333 !important;
} 