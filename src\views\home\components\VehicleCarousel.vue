<template>
  <div class="vehicle-carousel">
    <div class="panel-header">
      <h3 class="panel-title">车辆信息</h3>
      <div class="panel-subtitle">共 {{ vehiclesData.length }} 辆车</div>
    </div>
    
    <el-carousel 
      :interval="5000" 
      indicator-position="outside"
      height="150px"
      class="vehicle-cards-carousel"
    >
      <el-carousel-item 
        v-for="(chunk, pageIndex) in vehicleChunks" 
        :key="pageIndex"
      >
        <div class="vehicle-cards-page">
          <div 
            v-for="(vehicle, index) in chunk" 
            :key="vehicle.id" 
            class="vehicle-card"
            :class="getStatusClass(vehicle.status)"
          >
            <div class="card-header">
              <div class="vehicle-id">{{ vehicle.id }}</div>
              <div class="vehicle-status">{{ getStatusText(vehicle.status) }}</div>
            </div>
            
            <div class="card-content">
              <div class="vehicle-info">
                <div class="info-item">
                  <span class="info-label">电量:</span>
                  <div class="battery-indicator">
                    <div class="battery-bar">
                      <div 
                        class="battery-level" 
                        :class="getBatteryClass(vehicle.battery)"
                        :style="{ width: `${vehicle.battery}%` }"
                      ></div>
                    </div>
                    <span class="battery-text">{{ vehicle.battery }}%</span>
                  </div>
                </div>
                
                <div class="info-item">
                  <span class="info-label">速度:</span>
                  <span class="info-value">{{ vehicle.speed }} km/h</span>
                </div>
                
                <div class="info-item location">
                  <span class="info-label">位置:</span>
                  <span class="info-value">{{ vehicle.location }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 定义组件属性
const props = defineProps({
  vehiclesData: {
    type: Array,
    required: true
  }
});

// 计算数据分页
const vehicleChunks = computed(() => {
  // 每页显示3辆车
  const chunkSize = 3;
  const result = [];
  
  for (let i = 0; i < props.vehiclesData.length; i += chunkSize) {
    result.push(props.vehiclesData.slice(i, i + chunkSize));
  }
  
  return result;
});

// 状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'online': return '在线';
    case 'charging': return '充电中';
    case 'offline': return '离线';
    default: return '未知';
  }
};

// 状态样式类
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'online': return 'status-online';
    case 'charging': return 'status-charging';
    case 'offline': return 'status-offline';
    default: return '';
  }
};

// 电量样式类
const getBatteryClass = (battery: number): string => {
  if (battery <= 20) return 'battery-low';
  if (battery <= 50) return 'battery-medium';
  return 'battery-high';
};
</script>

<style lang="less" scoped>
.vehicle-carousel {
  background: rgba(0, 21, 41, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(24, 144, 255, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.85);
}

.panel-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.45);
}

.vehicle-cards-carousel {
  :deep(.el-carousel__indicators) {
    bottom: 0px;
  }
  
  :deep(.el-carousel__indicator) {
    padding: 0 4px;
    
    button {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.3);
    }
  }
  
  :deep(.el-carousel__indicator.is-active button) {
    background-color: rgba(24, 144, 255, 0.8);
  }
}

.vehicle-cards-page {
  display: flex;
  gap: 15px;
  padding: 15px;
  height: 100%;
}

.vehicle-card {
  flex: 1;
  background: rgba(0, 21, 41, 0.5);
  border-radius: 6px;
  padding: 12px;
  border-left: 3px solid #1890ff;
  display: flex;
  flex-direction: column;
  
  &.status-online {
    border-color: #52c41a;
  }
  
  &.status-charging {
    border-color: #faad14;
  }
  
  &.status-offline {
    border-color: #f5222d;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.vehicle-id {
  font-size: 16px;
  font-weight: 500;
}

.vehicle-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  .status-online & {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
    border: 1px solid rgba(82, 196, 26, 0.2);
  }
  
  .status-charging & {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
    border: 1px solid rgba(250, 173, 20, 0.2);
  }
  
  .status-offline & {
    background: rgba(245, 34, 45, 0.1);
    color: #f5222d;
    border: 1px solid rgba(245, 34, 45, 0.2);
  }
}

.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.vehicle-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  font-size: 12px;
  display: flex;
  align-items: center;
  
  &.location {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.info-label {
  color: rgba(255, 255, 255, 0.65);
  width: 42px;
  flex-shrink: 0;
}

.info-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.battery-indicator {
  display: flex;
  align-items: center;
  flex: 1;
}

.battery-bar {
  flex: 1;
  height: 8px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-right: 8px;
}

.battery-level {
  height: 100%;
  
  &.battery-low {
    background: linear-gradient(to right, #f5222d, #ff7875);
  }
  
  &.battery-medium {
    background: linear-gradient(to right, #faad14, #ffc53d);
  }
  
  &.battery-high {
    background: linear-gradient(to right, #52c41a, #95de64);
  }
}

.battery-text {
  width: 36px;
  text-align: right;
  
  .status-online & {
    color: #52c41a;
  }
  
  .status-charging & {
    color: #faad14;
  }
  
  .status-offline & {
    color: #f5222d;
  }
}
</style> 