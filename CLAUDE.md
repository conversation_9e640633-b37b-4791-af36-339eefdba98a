# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

- `npm run dev` - Start development server (runs on port 8001)
- `npm run build` - Build for production (runs typecheck then builds)
- `npm run build:prod` - Same as build, with production mode
- `npm run serve` - Build and preview production version
- `npm run lint` - Run ESLint on Vue and TypeScript files with auto-fix

## Architecture Overview

This is a Vue 3 vehicle monitoring system frontend built with Vite, TypeScript, and Element Plus. The system provides real-time vehicle tracking, status monitoring, and data visualization using Amap (高德地图).

### Core Technologies
- **Frontend**: Vue 3 + TypeScript + Vite
- **UI Framework**: Element Plus with Chinese locale
- **State Management**: Pinia
- **Routing**: Vue Router with hash mode
- **Maps**: Amap (高德地图) API integration
- **Real-time Communication**: Native WebSocket connections
- **Build Tool**: Vite with TypeScript compilation

### Key Architectural Patterns

**Dynamic Route Registration**: The system uses dynamic route registration that automatically maps URL paths to Vue components in `/src/views/`. Routes are registered at runtime based on user permissions and available views.

**WebSocket Service Architecture**: Real-time vehicle data is handled through a dedicated WebSocket service (`vehicleService.ts`) that manages connections, reconnections, and data broadcasting to components.

**Component-Based Design**: The main dashboard is split into reusable components:
- `StatusCards` - Vehicle status overview
- `VehicleMap` - Amap integration for vehicle tracking  
- `MonitorArea` - Camera monitoring interface
- `VehicleCarousel` - Rotating vehicle information display
- `TaskList` - Task management interface

**Service Layer**: Business logic is separated into service classes:
- `vehicleService.ts` - WebSocket management and vehicle data
- `mapService.ts` - Map operations and utilities
- `infoWindowService.ts` - Map popup management
- `baseService.ts` - HTTP client wrapper

### Development Patterns

**File Organization**:
- `/src/views/` - Page components (auto-registered as routes)
- `/src/components/` - Reusable UI components  
- `/src/services/` - Business logic services
- `/src/utils/` - Utility functions
- `/src/store/` - Pinia state management
- `/src/types/` - TypeScript type definitions

**Custom Components**: The project includes several custom Element Plus extensions:
- `ren-dept-tree` - Department tree selector
- `ren-radio-group` - Enhanced radio group
- `ren-region-tree` - Region tree selector  
- `ren-select` - Enhanced select component

**Authentication Flow**: Uses token-based authentication with automatic route protection. Tokens are stored in cache and attached to all HTTP requests via interceptors.

**Error Handling**: Centralized error handling through HTTP interceptors with user-friendly Chinese error messages and automatic login redirects for 401 errors.

## WebSocket Integration

The system relies heavily on WebSocket connections for real-time vehicle data. The connection URL format is `ws://localhost:10001/raw-ws`. Handle reconnection logic and connection failures gracefully.

## Build Configuration

The project uses custom Vite configuration with:
- SVG icon plugin for asset management
- Path aliases (`@/` maps to `src/`)
- Manual chunk splitting for better performance
- TypeScript strict mode enabled
- Development server on port 8001 with HMR

## Important Notes

- All file paths use `@/` alias instead of relative imports
- Chinese language support is configured throughout the UI
- The system expects specific WebSocket message formats for vehicle data
- Map integration requires valid Amap API keys
- Routes are dynamically registered based on file system structure in `/src/views/`