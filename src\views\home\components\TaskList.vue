<template>
  <div class="task-list-container">
    <div class="panel-header">
      <h3 class="panel-title">任务列表</h3>
      <div class="panel-actions">
        <button class="action-button">
          <i class="action-icon">+</i>
          添加任务
        </button>
      </div>
    </div>
    
    <div class="task-list">
      <div 
        v-for="(task, index) in tasksData" 
        :key="index" 
        class="task-item"
        :class="{ 'completed': task.status === 'completed' }"
      >
        <div class="task-time">{{ task.time }}</div>
        <div class="task-content">
          <div class="task-location">{{ task.location }}</div>
          <div class="task-type" :class="getTaskTypeClass(task.type)">{{ task.type }}</div>
        </div>
        <div class="task-info">
          <div class="task-handler">{{ task.handler }}</div>
          <div class="task-status" :class="getStatusClass(task.status)">
            {{ getStatusText(task.status) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件属性
const props = defineProps({
  tasksData: {
    type: Array,
    required: true
  }
});

// 获取任务类型样式类
const getTaskTypeClass = (type: string): string => {
  switch (type) {
    case '例行巡逻': return 'type-patrol';
    case '设备维护': return 'type-maintenance';
    case '安全检查': return 'type-security';
    case '应急响应': return 'type-emergency';
    default: return '';
  }
};

// 获取状态样式类
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'completed': return 'status-completed';
    case 'processing': return 'status-processing';
    case 'pending': return 'status-pending';
    default: return '';
  }
};

// 获取状态文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'completed': return '已完成';
    case 'processing': return '进行中';
    case 'pending': return '待处理';
    default: return '未知';
  }
};
</script>

<style lang="less" scoped>
.task-list-container {
  background: rgba(0, 21, 41, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(24, 144, 255, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  height: 270px;
  display: flex;
  flex-direction: column;
  margin-top: 15px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
}

.panel-title {
  margin: 0;
  font-size: 16px;
  font-weight: normal;
  color: rgba(255, 255, 255, 0.85);
}

.panel-actions {
  display: flex;
  gap: 10px;
}

.action-button {
  background: rgba(24, 144, 255, 0.1);
  border: 1px solid rgba(24, 144, 255, 0.3);
  border-radius: 4px;
  color: rgba(255, 255, 255, 0.85);
  font-size: 12px;
  padding: 4px 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(24, 144, 255, 0.2);
    border-color: rgba(24, 144, 255, 0.5);
  }
  
  .action-icon {
    margin-right: 4px;
    font-style: normal;
  }
}

.task-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(24, 144, 255, 0.3);
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }
}

.task-item {
  padding: 10px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.completed {
    opacity: 0.6;
  }
}

.task-time {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.65);
}

.task-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-location {
  font-size: 14px;
  font-weight: 500;
}

.task-type {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.type-patrol {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
    border: 1px solid rgba(82, 196, 26, 0.2);
  }
  
  &.type-maintenance {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    border: 1px solid rgba(24, 144, 255, 0.2);
  }
  
  &.type-security {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
    border: 1px solid rgba(250, 173, 20, 0.2);
  }
  
  &.type-emergency {
    background: rgba(245, 34, 45, 0.1);
    color: #f5222d;
    border: 1px solid rgba(245, 34, 45, 0.2);
  }
}

.task-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-handler {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.65);
}

.task-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
  
  &.status-completed {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
    border: 1px solid rgba(82, 196, 26, 0.2);
  }
  
  &.status-processing {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    border: 1px solid rgba(24, 144, 255, 0.2);
  }
  
  &.status-pending {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
    border: 1px solid rgba(250, 173, 20, 0.2);
  }
}
</style> 