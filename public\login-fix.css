/* 登录页样式美化 */

/* 登录背景渐变 */
.rr-login.deep-blue-theme {
  background: linear-gradient(135deg, #0A1F3D 0%, #142F53 50%, #1A3C6A 100%);
}

/* 登录表单样式 */
.rr-login-right .el-input__inner {
  color: #333 !important;
}

.rr-login-right .el-input__wrapper {
  background-color: transparent !important;
  box-shadow: 0 0 0 1px #dcdfe6 !important;
  padding-left: 8px !important;
}

.rr-login-right .el-input__prefix {
  padding-right: 0 !important;
  margin-right: 0 !important;
}

.rr-login-right .el-input__prefix + .el-input__inner {
  padding-left: 4px !important;
}

.rr-login-right .el-input__inner::placeholder {
  padding-left: 0 !important;
}

.rr-login-right .el-input.is-focus .el-input__wrapper {
  box-shadow: 0 0 0 1px #409EFF !important;
}

/* 登录表单前缀图标颜色设置为黑色 */
.rr-login-right .el-input__prefix-inner .el-icon,
.rr-login-right .el-input__prefix .el-icon {
  color: #333 !important;
  font-size: 15px !important;
  margin-right: 5px !important;
}

.rr-login-right .el-icon svg {
  fill: #333 !important;
  color: #333 !important;
  width: 15px !important;
  height: 15px !important;
}

/* 针对特定前缀图标 */
.rr-login-right [prefix-icon="user"] .el-input__prefix .el-icon,
.rr-login-right [prefix-icon="lock"] .el-input__prefix .el-icon,
.rr-login-right [prefix-icon="first-aid-kit"] .el-input__prefix .el-icon {
  color: #333 !important;
  margin-right: 2px !important;
}

.rr-login-right [prefix-icon="user"] .el-input__prefix .el-icon svg,
.rr-login-right [prefix-icon="lock"] .el-input__prefix .el-icon svg,
.rr-login-right [prefix-icon="first-aid-kit"] .el-input__prefix .el-icon svg {
  fill: #333 !important;
}

/* Element Plus新版本的图标样式 */
.rr-login-right .el-input__prefix .el-icon-user,
.rr-login-right .el-input__prefix .el-icon-lock,
.rr-login-right .el-input__prefix .el-icon-first-aid-kit {
  color: #333 !important;
}

/* 针对特定DOM路径的精确样式 */
.rr-login-wrap .rr-login-right form .el-form-item .el-input .el-input__prefix span i,
.rr-login-wrap .rr-login-right form .el-form-item .el-input .el-input__prefix i,
#app .rr-login-wrap .rr-login-right form .el-form-item .el-input .el-input__prefix span i,
#app div .rr-login-wrap .rr-login-right div form .el-form-item div div.el-input div span.el-input__prefix span i {
  color: #333 !important;
}

/* 覆盖所有可能的SVG图标样式 */
.rr-login-right .el-input__prefix i svg path,
.rr-login-right .el-input__prefix span i svg path,
.rr-login-right .el-input__prefix .el-icon svg path,
.rr-login-right .el-input__prefix span .el-icon svg path {
  fill: #333 !important;
}

/* 强制覆盖任何内联样式 */
.rr-login-right .el-input__prefix *[style] {
  color: #333 !important;
}

.rr-login-right .el-input__prefix * svg[style] {
  fill: #333 !important;
}

/* 确保登录按钮样式 */
.rr-login-right-main-btn {
  width: 100%;
  border-radius: 4px;
}

/* 登录页标题样式 */
.rr-login-right-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
  font-weight: bold;
}

/* 登录页版权信息 */
.rr-login-right-footer {
  margin-top: 20px;
  text-align: center;
  color: #999;
  font-size: 12px;
} 