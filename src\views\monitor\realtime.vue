<template>
  <div class="mod-monitor__realtime">
    <div class="video-grid-container">
      <div class="video-grid">
        <div v-for="(stream, index) in currentPageStreams" :key="stream.id" class="video-item">
          <div class="video-card">
            <div class="video-header">
              <span class="video-title">{{ stream.name }}</span>
              <el-tag size="small" :type="getStreamStatusType(stream)" class="video-status">
                {{ getStreamStatusText(stream) }}
              </el-tag>
            </div>
            <div class="video-content">
              <keep-alive>
                <MpegtsPlayer
                  :stream-url="stream.url"
                  :camera-info="stream.name"
                  :autoplay="true"
                  @error="handlePlayerError(index, $event)"
                  @status-change="handlePlayerStatusChange(index, stream.id, $event)"
                />
              </keep-alive>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页控件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[4, 8, 16, 32]"
        :page-size="pageSize"
        :total="streamList.length"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, toRefs, onBeforeUnmount, defineAsyncComponent } from 'vue';
import { ElNotification } from 'element-plus';
// 使用异步组件导入，避免类型错误
const MpegtsPlayer = defineAsyncComponent(() => import('@/components/MpegtsPlayer.vue'));
import useView from "@/hooks/useView";

// 视频流列表
const streamList = ref<any[]>([]);
const currentPage = ref(1);
const pageSize = ref(16); // 默认每页显示16个视频
const streamStatuses = ref<Record<string, string>>({});

// useView定义 - 用于调用API
const view = reactive({
  getDataListURL: "/monitor/stream/list",
  getDataListIsPage: false, // 非分页接口
  dataForm: {
    key: ''
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 计算当前页的视频流
const currentPageStreams = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  return streamList.value.slice(startIndex, startIndex + pageSize.value);
});

// 处理页码变化
const handleCurrentChange = (page: number) => {
  currentPage.value = page;
};

// 处理每页条数变化
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  // 如果当前页码超出范围，重置为第一页
  if (currentPage.value > Math.ceil(streamList.value.length / pageSize.value)) {
    currentPage.value = 1;
  }
};

// 处理播放器错误
const handlePlayerError = (index: number, error: any) => {
  const stream = currentPageStreams.value[index];
  if (stream) {
    console.error(`视频流 ${stream.name} 播放错误:`, error);
    streamStatuses.value[stream.id] = 'error';
    
    ElNotification({
      title: '视频流连接失败',
      message: `视频流 ${stream.name} 连接失败，请检查网络或刷新页面`,
      type: 'error',
      duration: 3000
    });
  }
};

// 处理播放器状态变化
const handlePlayerStatusChange = (index: number, streamId: string, status: string) => {
  streamStatuses.value[streamId] = status;
  
  if (status === 'playing') {
    const stream = currentPageStreams.value[index];
    console.log(`视频流 ${stream.name} 开始播放`);
  }
};

// 获取流状态类型
const getStreamStatusType = (stream: any) => {
  const status = streamStatuses.value[stream.id];
  
  switch (status) {
    case 'playing':
      return 'success';
    case 'error':
      return 'danger';
    case 'loading':
    default:
      return 'info';
  }
};

// 获取流状态文本
const getStreamStatusText = (stream: any) => {
  const status = streamStatuses.value[stream.id];
  
  switch (status) {
    case 'playing':
      return '播放中';
    case 'error':
      return '连接失败';
    case 'loading':
    default:
      return '加载中';
  }
};

// 监听useView中的数据变化
const originalGetDataList = state.getDataList;
state.getDataList = async () => {
  await originalGetDataList();
  // 从state.dataList获取数据更新本地状态
  if (state.dataList && Array.isArray(state.dataList)) {
    streamList.value = state.dataList;
    // 初始化或更新所有流的状态
    streamList.value.forEach(stream => {
      if (!streamStatuses.value[stream.id]) {
        streamStatuses.value[stream.id] = 'loading';
      }
    });
    
    // 数据加载成功通知
    // ElNotification({
    //   title: '连接成功',
    //   message: `已成功获取 ${streamList.value.length} 个视频流`,
    //   type: 'success',
    //   duration: 3000
    // });
  }
};

// 定时刷新视频流列表（每30秒刷新一次）
let refreshTimer: number | null = null;

onMounted(() => {
  // 获取视频流列表
  state.getDataList();
  
  // 设置定时刷新
  refreshTimer = window.setInterval(() => {
    state.getDataList();
  }, 30000);
});

onBeforeUnmount(() => {
  // 清除定时器
  if (refreshTimer !== null) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
});
</script>

<style lang="less" scoped>
.mod-monitor__realtime {
  height: 92vh;
  display: flex;
  flex-direction: column;
  background-color: #001529;
  color: white;
  padding: 16px;
  
  .video-grid-container {
    flex: 1;
    overflow: auto;
    
    .video-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 16px;
      padding-bottom: 16px;
      
      @media (max-width: 1400px) {
        grid-template-columns: repeat(3, 1fr);
      }
      
      @media (max-width: 1100px) {
        grid-template-columns: repeat(2, 1fr);
      }
      
      @media (max-width: 768px) {
        grid-template-columns: 1fr;
      }
      
      .video-item {
        aspect-ratio: 16/9;
        min-height: 180px;
        
        .video-card {
          height: 100%;
          background: linear-gradient(135deg, rgba(0, 21, 41, 0.8), rgba(0, 30, 60, 0.7));
          border: 1px solid rgba(24, 144, 255, 0.2);
          border-radius: 8px;
          overflow: hidden;
          display: flex;
          flex-direction: column;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
            border-color: rgba(24, 144, 255, 0.4);
          }
          
          .video-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: rgba(0, 0, 0, 0.3);
            border-bottom: 1px solid rgba(24, 144, 255, 0.2);
            
            .video-title {
              font-size: 14px;
              font-weight: 500;
              color: white;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
            }
            
            .video-status {
              font-size: 12px;
            }
          }
          
          .video-content {
            flex: 1;
            background: #000;
            position: relative;
            overflow: hidden;
          }
        }
      }
    }
  }
  
  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    padding: 8px 0;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }
}
</style>