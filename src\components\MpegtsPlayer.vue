<template>
  <div class="mpegts-player-container">
    <!-- 视频元素 -->
    <video ref="videoElement" class="mpegts-player" controls muted></video>
    
    <!-- 加载中覆盖层 -->
    <div v-if="status === 'loading'" class="video-loading-overlay">
      <div class="loading-spinner"></div>
      <div class="loading-text">{{ loadingText }}</div>
    </div>
    
    <!-- 错误覆盖层 -->
    <div v-if="status === 'error'" class="video-error-overlay">
      <div class="error-icon">
        <el-icon><Warning /></el-icon>
      </div>
      <div class="error-text">{{ errorText }}</div>
      <el-button size="small" type="primary" @click="retry">
        <el-icon><Refresh /></el-icon>
        重试
      </el-button>
    </div>
    
    <!-- 播放按钮覆盖层 -->
    <div v-if="status === 'waiting'" class="video-play-overlay" @click="play">
      <div class="play-button">
        <svg viewBox="0 0 24 24" width="48" height="48">
          <path fill="#fff" d="M8 5v14l11-7z"/>
        </svg>
      </div>
      <div class="play-text">点击播放</div>
    </div>
    
    <!-- 摄像头信息覆盖层 -->
    <div class="camera-overlay" v-if="status !== 'error'">
      <div class="camera-info">
        <span>{{ cameraInfo }}</span>
        <span>时间: {{ currentTime }}</span>
      </div>
      <div class="camera-status">
        <el-icon>
          <VideoCamera />
        </el-icon>
        <span class="rec-dot"></span>
        <span>REC</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, computed, nextTick, onActivated, onDeactivated } from 'vue';
import { Warning, Refresh, VideoCamera } from '@element-plus/icons-vue';
import mpegts from 'mpegts.js';
import { ElNotification } from 'element-plus';

// 接收的属性
const props = defineProps({
  // 流媒体URL
  streamUrl: {
    type: String,
    required: true
  },
  // 摄像头信息
  cameraInfo: {
    type: String,
    default: '实时监控'
  },
  // 加载文本
  loadingText: {
    type: String,
    default: '正在连接视频流...'
  },
  // 错误文本
  errorText: {
    type: String,
    default: '视频流连接失败'
  },
  // 摄像头类型（用于不同滤镜效果）
  cameraType: {
    type: String,
    default: 'default' // 可选值: default, type-2, type-3
  },
  // 自动启动播放
  autoplay: {
    type: Boolean,
    default: true
  }
});

// 事件
const emit = defineEmits(['status-change', 'player-ready', 'error']);

// 组件状态
const status = ref('loading'); // loading, playing, error, waiting
const videoElement = ref<HTMLVideoElement | null>(null);
const player = ref<any>(null);
const currentTime = ref('00:00:00');
const timerId = ref<number | null>(null);

// 计算当前播放器类名
const playerClass = computed(() => {
  return {
    'mpegts-player': true,
    [props.cameraType]: props.cameraType !== 'default'
  };
});

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");
  currentTime.value = `${hours}:${minutes}:${seconds}`;
};

// 播放视频
const play = async () => {
  if (!videoElement.value) return;
  
  try {
    // 确保音频静音（避免自动播放策略阻止）
    videoElement.value.muted = true;
    
    // 尝试播放
    await videoElement.value.play();
    status.value = 'playing';
  } catch (error) {
    console.error('播放失败:', error);
    status.value = 'waiting';
  }
};

// 重试连接
const retry = () => {
  status.value = 'loading';
  destroyPlayer();
  setupPlayer();
};

// 销毁播放器
const destroyPlayer = () => {
  if (player.value) {
    try {
      player.value.pause();
      player.value.unload();
      player.value.detachMediaElement();
      player.value.destroy();
      player.value = null;
      console.log('播放器已销毁');
    } catch (error) {
      console.error('销毁播放器时出错:', error);
    }
  }
};

// 设置播放器
const setupPlayer = () => {
  if (!videoElement.value) {
    console.error('未找到视频元素');
    status.value = 'error';
    emit('error', new Error('未找到视频元素'));
    return;
  }
  
  // 检查浏览器是否支持 mpegts.js
  if (!mpegts.isSupported()) {
    status.value = 'error';
    ElNotification({
      title: '不支持的浏览器',
      message: '您的浏览器不支持MPEG-TS视频播放，请使用Chrome、Firefox或Edge等现代浏览器',
      type: 'error',
      duration: 5000
    });
    emit('error', new Error('浏览器不支持 MPEG-TS'));
    return;
  }
  
  try {
    // 创建播放器配置
    const playerConfig = {
      enableWorker: true,
      enableStashBuffer: false,
      stashInitialSize: 128,
      // 降低延迟设置
      liveBufferLatencyChasing: true,
      liveBufferLatencyMaxLatency: 2.0,
      liveBufferLatencyMinRemain: 0.1,
      // 其他配置
      seekStage: true,
      seekHandler: {
        connect_timeout: 8000,
        read_timeout: 8000
      }
    };
    
    // 创建新的 mpegts.js 播放器实例
    player.value = mpegts.createPlayer({
      type: 'mse', // 使用 MSE 播放
      url: props.streamUrl,
      isLive: true,
      cors: true,
      withCredentials: false
    }, playerConfig);
    
    // 将播放器附加到 video 元素
    player.value.attachMediaElement(videoElement.value);
    
    // 监听播放器事件
    player.value.on(mpegts.Events.ERROR, (errorType: string, errorDetail: string) => {
      console.error(`播放器错误:`, errorType, errorDetail);
      status.value = 'error';
      emit('error', { type: errorType, detail: errorDetail });
    });
    
    player.value.on(mpegts.Events.MEDIA_INFO, (mediaInfo: any) => {
      console.log(`获取到媒体信息:`, mediaInfo);
    });
    
    player.value.on(mpegts.Events.STATISTICS_INFO, (stats: any) => {
    //   console.log(`播放器统计信息:`, stats);
    });
    
    // 开始加载
    player.value.load();
    
    // 通知播放器已准备好
    emit('player-ready', player.value);
    
    // 设置超时检测
    setTimeout(() => {
      if (status.value === 'loading') {
        status.value = 'error';
        emit('error', new Error('连接超时'));
      }
    }, 15000); // 15秒超时
    
    // 监听 video 元素事件
    if (videoElement.value) {
      // 加载元数据事件
      videoElement.value.addEventListener('loadedmetadata', () => {
        console.log('视频元数据已加载');
        if (props.autoplay) {
          play();
        } else {
          status.value = 'waiting';
        }
      });
      
      // 播放事件
      videoElement.value.addEventListener('playing', () => {
        console.log('视频已开始播放');
        status.value = 'playing';
        emit('status-change', 'playing');
      });
      
      // 错误事件
      videoElement.value.addEventListener('error', (e) => {
        console.error('视频元素错误:', e);
        status.value = 'error';
        emit('error', e);
      });
    }
  } catch (error) {
    console.error('设置播放器时出错:', error);
    status.value = 'error';
    emit('error', error);
  }
};

// 监听状态变化
watch(status, (newStatus) => {
  emit('status-change', newStatus);
});

// 监听 URL 变化
watch(() => props.streamUrl, (newUrl, oldUrl) => {
  if (newUrl !== oldUrl) {
    console.log(`视频源已变更: ${oldUrl} -> ${newUrl}`);
    destroyPlayer();
    status.value = 'loading';
    nextTick(() => {
      setupPlayer();
    });
  }
});

// 组件挂载时初始化
onMounted(() => {
  updateCurrentTime();
  // 启动时间更新计时器
  timerId.value = window.setInterval(updateCurrentTime, 1000);
  
  // 初始化播放器
  setupPlayer();
});

// 组件被 keep-alive 激活时
onActivated(() => {
  if (player.value && status.value === 'playing') {
    // 恢复播放
    play();
  }
});

// 组件被 keep-alive 停用时
onDeactivated(() => {
  if (player.value) {
    // 保存当前播放状态
    const wasPlaying = status.value === 'playing';
    if (wasPlaying) {
      // 暂停播放但不销毁播放器
      videoElement.value?.pause();
    }
  }
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
  // 清除时间更新定时器
  if (timerId.value) {
    clearInterval(timerId.value);
  }
  
  // 销毁播放器
  destroyPlayer();
});

// 暴露组件方法供父组件调用
defineExpose({
  play,
  retry,
  getStatus: () => status.value,
  getPlayer: () => player.value
});
</script>

<style lang="less" scoped>
.mpegts-player-container {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  overflow: hidden;
}

.mpegts-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 为不同类型的摄像头添加特效
.type-2 {
  filter: contrast(1.1) saturate(1.2);
}

.type-3 {
  filter: hue-rotate(30deg) contrast(1.2);
}

// 加载中覆盖层
.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 21, 41, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
  
  .loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(24, 144, 255, 0.3);
    border-radius: 50%;
    border-top-color: #1890ff;
    animation: spin 1s ease-in-out infinite;
    margin-bottom: 8px;
  }
  
  .loading-text {
    color: #fff;
    font-size: 12px;
  }
}

// 错误覆盖层
.video-error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 21, 41, 0.7);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 2;
  
  .error-icon {
    color: #ff4d4f;
    font-size: 20px;
    margin-bottom: 8px;
  }
  
  .error-text {
    color: #fff;
    font-size: 12px;
    margin-bottom: 10px;
  }
}

// 播放按钮覆盖层
.video-play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 3;
  cursor: pointer;
  
  .play-button {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: rgba(24, 144, 255, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    transition: transform 0.2s, background-color 0.2s;
    
    svg {
      margin-left: 4px; /* 稍微向右偏移，使播放图标视觉上居中 */
    }
    
    &:hover {
      transform: scale(1.1);
      background-color: rgba(24, 144, 255, 0.9);
    }
  }
  
  .play-text {
    color: #fff;
    font-size: 12px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.6);
  }
}

// 摄像头信息覆盖层
.camera-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 10px;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  pointer-events: none; /* 确保覆盖层不会阻挡视频的交互 */

  .camera-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
    background-color: rgba(0, 0, 0, 0.3);
    padding: 3px 6px;
    border-radius: 4px;
    margin-right: 5px;
  }

  .camera-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    background-color: rgba(0, 0, 0, 0.3);
    padding: 3px 6px;
    border-radius: 4px;
    align-self: flex-end;

    .rec-dot {
      width: 8px;
      height: 8px;
      background-color: #ff4d4f;
      border-radius: 50%;
      animation: blink 1s infinite;
    }
  }
}

// 动画
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}
</style> 