<template>
  <div class="rr-login deep-blue-theme">
    <!-- 科技感背景元素 -->
    <div class="tech-background">
      <div class="grid-lines"></div>
      <div class="particles-container" id="particles-js"></div>
      <div class="scan-line"></div>
    </div>
    
    <div class="decoration-circle decoration-circle-1"></div>
    <div class="decoration-circle decoration-circle-2"></div>
    <div class="decoration-circle decoration-circle-3"></div>
    
    <div class="rr-login-wrap">
      <div class="rr-login-left hidden-sm-and-down">
        <!-- 美化的左侧区域 -->
        <div class="tech-glow-container">
          <div class="tech-circle"></div>
          <div class="tech-hexagon"></div>
          <div class="tech-line tech-line-1"></div>
          <div class="tech-line tech-line-2"></div>
          <div class="tech-dot tech-dot-1"></div>
          <div class="tech-dot tech-dot-2"></div>
          <div class="tech-dot tech-dot-3"></div>
        </div>
        
        <div class="logo-area">
          <div class="logo-icon">
            <svg width="40" height="40" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M24 6C14.0589 6 6 14.0589 6 24C6 33.9411 14.0589 42 24 42C33.9411 42 42 33.9411 42 24" stroke="#38AEF3" stroke-width="2" stroke-linecap="round"/>
              <path d="M24 14V24L32 28" stroke="#38AEF3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M32 12L42 24L36 30" stroke="#38AEF3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </div>
        
        <p class="rr-login-left-title">智能巡逻指挥系统</p>
        <div class="rr-login-left-subtitle">科技赋能 · 智慧巡防 · 高效指挥</div>
        
        <div class="tech-data-visualization">
          <div class="data-line data-line-1"></div>
          <div class="data-line data-line-2"></div>
          <div class="data-line data-line-3"></div>
        </div>
        
        <div class="tech-bottom-decor">
          <div class="tech-radar"></div>
        </div>
      </div>

      <div class="rr-login-right">
        <div class="rr-login-right-main">
          <h4 class="rr-login-right-main-title font-title">登录</h4>
          <el-form ref="formRef" label-width="80px" :status-icon="true" :model="login" :rules="rules" @keyup.enter="onLogin">
            <el-form-item label-width="0" prop="username">
              <el-input v-model="login.username" placeholder="用户名" prefix-icon="user" autocomplete="off" style="--el-input-text-color: #333;"></el-input>
            </el-form-item>
            <el-form-item label-width="0" prop="password">
              <el-input placeholder="密码" v-model="login.password" prefix-icon="lock" autocomplete="off" show-password style="--el-input-text-color: #333;"></el-input>
            </el-form-item>
            <el-form-item label-width="0" prop="captcha">
              <el-space class="rr-login-right-main-code">
                <el-input v-model="login.captcha" placeholder="验证码" prefix-icon="first-aid-kit" style="--el-input-text-color: #333;"></el-input>
                <img style="vertical-align: middle; height: 40px; cursor: pointer; border-radius: 4px;" :src="state.captchaUrl" @click="onRefreshCode" alt="" />
              </el-space>
            </el-form-item>
            <el-form-item label-width="0">
              <el-button type="primary" size="small" :disabled="state.loading" @click="onLogin" class="rr-login-right-main-btn"> 登录 </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div class="login-footer">
      <p>© {{ state.year }} 智能巡逻指挥系统 - 版权所有 | 技术支持：成都研发中心</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref, onBeforeMount } from "vue";
import { CacheToken } from "@/constants/cacheKey";
import baseService from "@/service/baseService";
import { setCache } from "@/utils/cache";
import { ElMessage } from "element-plus";
import { getUuid } from "@/utils/utils";
import app from "@/constants/app";
import SvgIcon from "@/components/base/svg-icon/index";
import { useAppStore } from "@/store";
import { useRouter } from "vue-router";

const store = useAppStore();
const router = useRouter();

const state = reactive({
  captchaUrl: "",
  loading: false,
  year: new Date().getFullYear()
});

const login = reactive({ username: "", password: "", captcha: "", uuid: "" });

// 粒子效果加载状态
const particlesLoaded = ref(false);

// 尝试多种方式加载粒子效果库
onBeforeMount(() => {
  // 首先尝试从CDN加载（最可靠的方式）
  // loadParticlesJS('https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js');
  loadParticlesJS('/js/particles.min.js');
});

// 加载粒子效果库的函数
const loadParticlesJS = (src) => {
  if (particlesLoaded.value) return;
  
  console.log('正在尝试加载粒子效果库:', src);
  const script = document.createElement('script');
  script.src = src;
  script.async = true;
  
  script.onload = () => {
    console.log('粒子效果库加载成功:', src);
    particlesLoaded.value = true;
    
    // 延迟一点执行初始化，确保库完全加载
    setTimeout(() => {
      initParticles();
    }, 10);
  };
  
  script.onerror = () => {
    console.error('粒子效果库加载失败:', src);
    
    // 如果是CDN失败，尝试本地路径
    if (src.includes('cdn')) {
      console.log('尝试从本地加载粒子效果库');
      loadParticlesJS('/js/particles.min.js');
    } else {
      // 如果本地也失败，尝试另一个CDN
      loadParticlesJS('https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js');
    }
  };
  
  document.head.appendChild(script);
};

// 初始化粒子效果
const initParticles = () => {
  // 检查 particlesJS 是否可用
  if (typeof window.particlesJS === 'function') {
    console.log('正在初始化粒子效果');
    try {
      window.particlesJS('particles-js', {
        particles: {
          number: { value: 80, density: { enable: true, value_area: 800 } },
          color: { value: "#38AEF3" },
          shape: { type: "circle" },
          opacity: { value: 0.5, random: true },
          size: { value: 3, random: true },
          line_linked: {
            enable: true,
            distance: 150,
            color: "#38AEF3",
            opacity: 0.4,
            width: 1
          },
          move: {
            enable: true,
            speed: 2,
            direction: "none",
            random: true,
            out_mode: "out"
          }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: { enable: true, mode: "grab" },
            resize: true
          },
          modes: {
            grab: { distance: 140, line_linked: { opacity: 0.8 } }
          }
        }
      });
      console.log('粒子效果初始化成功');
    } catch (error) {
      console.error('初始化粒子效果时发生错误:', error);
    }
  } else {
    console.error('粒子效果库未正确加载，window.particlesJS 不可用');
  }
};

onMounted(() => {
  //清理数据
  store.logout();
  getCaptchaUrl();
  
  // 强制修复输入框文本颜色
  setTimeout(() => {
    const inputs = document.querySelectorAll('.rr-login-right .el-input__inner');
    inputs.forEach(input => {
      (input as HTMLElement).style.color = '#333';
    });
    
    // 添加强制样式
    const style = document.createElement('style');
    style.innerHTML = `
      .rr-login-right .el-input__inner {
        color: #333 !important;
      }
      .rr-login-right .el-input__wrapper {
        background-color: transparent !important;
        border-bottom: 1px solid #dcdfe6 !important;
        box-shadow: none !important;
      }
    `;
    document.head.appendChild(style);
  }, 100);
});

// TypeScript接口声明
declare global {
  interface Window {
    particlesJS: any;
  }
}

const formRef = ref();

const rules = ref({
  username: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  password: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  captcha: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const getCaptchaUrl = () => {
  login.uuid = getUuid();
  state.captchaUrl = `${app.api}/captcha?uuid=${login.uuid}`;
};

const onRefreshCode = () => {
  getCaptchaUrl();
};

const onLogin = () => {
  formRef.value.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      baseService
        .post("/login", login)
        .then((res) => {
          state.loading = false;
          if (res.code === 0) {
            setCache(CacheToken, res.data, true);
            ElMessage.success("登录成功");
            router.push("/");
          } else {
            ElMessage.error(res.msg);
          }
        })
        .catch(() => {
          state.loading = false;
          onRefreshCode();
        });
    }
  });
};
</script>

<style lang="less" scoped>
@import url("@/assets/theme/base.less");
@import url("@/assets/css/fonts.less");
@import url("@/assets/css/login-page.css");
@import url("@/assets/css/login-form-fix.css");

/* 定义科技风格的颜色变量 */
:root {
  --tech-primary: #38AEF3;
  --tech-secondary: #1E88E5;
  --tech-glow: rgba(56, 174, 243, 0.7);
  --tech-dark: #0A1F3D;
}

.rr-login {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0A1F3D 0%, #142F53 50%, #1A3C6A 100%);
  background-size: 200% 200%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: var(--font-content);
  overflow: hidden; /* 确保超出范围的元素被裁剪 */
  
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 30%, rgba(56, 174, 243, 0.15) 0%, transparent 70%);
    z-index: 0;
  }

  /* 科技感背景元素样式 */
  .tech-background {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 0;
    overflow: hidden;
    
    /* 网格线效果 */
    .grid-lines {
      position: absolute;
      width: 200%;
      height: 200%;
      top: -50%;
      left: -50%;
      background-image: 
        linear-gradient(to right, rgba(56, 174, 243, 0.07) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(56, 174, 243, 0.07) 1px, transparent 1px);
      background-size: 50px 50px;
      transform: perspective(500px) rotateX(60deg);
      animation: grid-move 20s linear infinite;
      z-index: -1;
    }
    
    /* 扫描线效果 */
    .scan-line {
      position: absolute;
      width: 100%;
      height: 5px;
      background: linear-gradient(90deg, transparent, var(--tech-glow), transparent);
      top: 50%;
      animation: scan-move 4s linear infinite;
      opacity: 0.5;
      z-index: -1;
    }
  }
  
  /* 粒子容器 */
  .particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
  }

  /* 响应式调整 */
  @media only screen and (max-width: 992px) {
    .rr-login-wrap {
      width: 96% !important;
      max-width: 450px;
    }
    .rr-login-right {
      width: 100% !important;
      padding: 30px 0;
    }
    
    .rr-login-right-main {
      width: 80% !important;
    }
  }
  
  @media only screen and (max-width: 576px) {
    .rr-login-wrap {
      width: 90% !important;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
    }
    
    .rr-login-right-main {
      width: 85% !important;
    }
    
    .login-footer {
      padding: 15px !important;
      
      p {
        font-size: 12px;
      }
    }
  }

  &-wrap {
    margin: 0 auto;
    width: 1000px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
    animation-duration: 1s;
    animation-fill-mode: both;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
    z-index: 1;
    backdrop-filter: blur(5px);
  }

  &-left {
    justify-content: center;
    flex-direction: column;
    background-color: rgba(10, 31, 61, 0.8);
    color: #fff;
    float: left;
    width: 50%;
    position: relative;
    /* 添加溢出隐藏，确保所有动画元素不会溢出 */
    overflow: hidden;
    
    /* 优化左侧装饰线 */
    &::after {
      content: "";
      position: absolute;
      top: 15%;
      left: 50%;
      width: 70%;
      height: 2px;
      background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.8), transparent);
      transform: translateX(-50%);
      animation: pulse 3s ease-in-out infinite;
    }
    
    &::before {
      content: "";
      position: absolute;
      bottom: 15%;
      left: 50%;
      width: 70%;
      height: 2px;
      background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.8), transparent);
      transform: translateX(-50%);
      animation: pulse 3s ease-in-out 1.5s infinite;
    }

    /* Logo区域样式 */
    .logo-area {
      position: absolute;
      top: 12%;
      left: 50%;
      transform: translateX(-50%);
      
      .logo-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto;
        background: rgba(14, 39, 74, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 0 20px rgba(56, 174, 243, 0.3);
        position: relative;
        
        &::before {
          content: '';
          position: absolute;
          width: 90px;
          height: 90px;
          border-radius: 50%;
          border: 1px solid rgba(56, 174, 243, 0.3);
          animation: pulse 2s infinite;
        }
        
        &::after {
          content: '';
          position: absolute;
          width: 100px;
          height: 100px;
          border-radius: 50%;
          border: 1px dashed rgba(56, 174, 243, 0.2);
          animation: rotate 30s linear infinite;
        }
        
        svg {
          width: 40px;
          height: 40px;
          fill: none;
          path {
            stroke-dasharray: 100;
            stroke-dashoffset: 100;
            animation: draw 3s forwards, glow 2s 2s infinite alternate;
          }
        }
      }
    }

    /* 科技装饰元素 */
    .tech-glow-container {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      pointer-events: none;
      
      .tech-circle {
        position: absolute;
        width: 120px;
        height: 120px;
        border: 1px solid rgba(56, 174, 243, 0.2);
        border-radius: 50%;
        top: 70%;
        left: 20%;
        animation: rotate 20s linear infinite;
        
        &::after {
          content: '';
          position: absolute;
          width: 6px;
          height: 6px;
          background: var(--tech-primary);
          border-radius: 50%;
          top: 50%;
          left: 0;
          transform: translate(-50%, -50%);
          box-shadow: 0 0 10px var(--tech-glow);
        }
      }
      
      .tech-hexagon {
        position: absolute;
        width: 80px;
        height: 80px;
        top: 30%;
        right: 15%;
        background: transparent;
        border: 1px solid rgba(56, 174, 243, 0.2);
        clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
        animation: pulse 4s ease-in-out infinite alternate, rotate 30s linear infinite;
      }
      
      .tech-line {
        position: absolute;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(56, 174, 243, 0.5), transparent);
        
        &.tech-line-1 {
          width: 140px;
          transform: rotate(30deg);
          top: 40%;
          left: 10%;
          animation: pulse 3s ease-in-out infinite;
        }
        
        &.tech-line-2 {
          width: 100px;
          transform: rotate(-45deg);
          bottom: 30%;
          right: 20%;
          animation: pulse 4s 1s ease-in-out infinite;
        }
      }
      
      .tech-dot {
        position: absolute;
        width: 4px;
        height: 4px;
        background: var(--tech-primary);
        border-radius: 50%;
        box-shadow: 0 0 8px var(--tech-glow);
        
        &.tech-dot-1 {
          top: 35%;
          left: 25%;
          animation: blink 3s ease-in-out infinite;
        }
        
        &.tech-dot-2 {
          bottom: 40%;
          right: 30%;
          animation: blink 2.5s 0.5s ease-in-out infinite;
        }
        
        &.tech-dot-3 {
          top: 55%;
          right: 15%;
          animation: blink 4s 1s ease-in-out infinite;
        }
      }
    }
    
    /* 数据可视化装饰 */
    .tech-data-visualization {
      position: absolute;
      bottom: 24%;
      left: 50%;
      transform: translateX(-50%);
      width: 60%;
      height: 40px;
      
      .data-line {
        position: absolute;
        height: 1px;
        background: var(--tech-primary);
        opacity: 0.6;
        width: 0;
        left: 0;
        
        &.data-line-1 {
          top: 0;
          animation: data-grow 3s ease-out forwards;
        }
        
        &.data-line-2 {
          top: 15px;
          animation: data-grow 3s 0.5s ease-out forwards;
        }
        
        &.data-line-3 {
          top: 30px;
          animation: data-grow 3s 1s ease-out forwards;
        }
        
        &::after {
          content: '';
          position: absolute;
          height: 5px;
          width: 5px;
          background: var(--tech-primary);
          border-radius: 50%;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          box-shadow: 0 0 5px var(--tech-glow);
        }
      }
    }
    
    /* 底部雷达装饰 */
    .tech-bottom-decor {
      position: absolute;
      bottom: 10%;
      left: 50%;
      transform: translateX(-50%);
      
      .tech-radar {
        width: 60px;
        height: 60px;
        border: 1px solid rgba(56, 174, 243, 0.4);
        border-radius: 50%;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 50%;
          height: 50%;
          background: linear-gradient(90deg, rgba(56, 174, 243, 0.7), transparent);
          transform-origin: 0 0;
          animation: radar-scan 4s linear infinite;
        }
      }
    }

    &-title {
      text-align: center;
      color: #fff;
      font-weight: 500;
      letter-spacing: 3px;
      font-size: 36px;
      font-family: var(--font-title);
      text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3), 0 0 20px rgba(56, 174, 243, 0.3);
      margin-top: 40px; /* 调整与logo的距离 */
      animation: title-glow 2s ease-in-out infinite alternate;
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        width: 50px;
        height: 2px;
        background: linear-gradient(90deg, transparent, var(--tech-primary), transparent);
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
      }
    }

    &-subtitle {
      text-align: center;
      color: rgba(255, 255, 255, 0.9);
      font-size: 18px;
      margin-top: 30px;
      letter-spacing: 2.5px;
      line-height: 1.5;
      font-weight: 400;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
      
      /* 文字发光特效 */
      background-image: linear-gradient(90deg, 
        rgba(255, 255, 255, 0.8) 0%, 
        rgba(56, 174, 243, 1) 50%, 
        rgba(255, 255, 255, 0.8) 100%);
      background-size: 200% auto;
      color: #fff;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      animation: shine 4s linear infinite;
    }
  }

  &-right {
    border-left: none;
    color: #fff;
    background-color: rgba(255, 255, 255, 0.95);
    width: 50%;
    float: left;

    &-main {
      margin: 0 auto;
      width: 65%;
      &-title {
        color: #162C4A;
        margin-bottom: 40px;
        font-weight: 600;
        font-size: 24px;
        text-align: center;
        letter-spacing: 4px;
        position: relative;
        
        &::after {
          content: "";
          position: absolute;
          bottom: -10px;
          left: 50%;
          width: 30px;
          height: 3px;
          background: #409EFF;
          transform: translateX(-50%);
          border-radius: 3px;
        }
      }

      &-lang .iconfont {
        font-size: 20px;
        color: #606266;
        font-weight: 800;
        width: 20px;
        height: 20px;
      }

      /* 表单元素样式优化 */
      :deep(.el-form-item) {
        margin-bottom: 25px;
      }

      :deep(.el-form-item__error) {
        color: #f56c6c;
        font-size: 12px;
        padding-top: 4px;
      }

      /* 调整输入框内部样式 */
      :deep(.el-input) {
        --el-input-text-color: #333;
        --el-input-placeholder-color: #909399;
        
        .el-input__wrapper {
          background-color: transparent;
          box-shadow: none !important;
          border: none;
          padding: 0;
          border-radius: 0;
          transition: all 0.3s;
          border-bottom: 1px solid #dcdfe6;
        }
        
        .el-input__inner {
          height: 40px;
          color: #333 !important; /* 强制使用深色文本 */
          padding-left: 30px; /* 为图标留出空间 */
        }
        
        .el-input__prefix {
          left: 0;
          color: #909399;
        }
        
        &.is-focus {
          .el-input__wrapper {
            border-bottom: 1px solid #409EFF;
            box-shadow: none !important;
          }
          
          .el-input__prefix {
            color: #409EFF;
          }
        }
        
        &:hover {
          .el-input__wrapper {
            border-bottom: 1px solid #409EFF;
          }
        }
      }
      
      /* 输入框背景色修复 */
      :deep(.el-input__wrapper) {
        background-color: transparent !important;
      }
      
      &-code {
        width: 100%;
        .el-space__item:first-child {
          flex: 1;
        }
        
        img {
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          transition: all 0.3s;
          
          &:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }
      }
      
      &-btn {
        width: 100%;
        height: 45px;
        font-size: 18px !important;
        letter-spacing: 2px;
        font-weight: 400 !important;
        cursor: pointer;
        margin-top: 30px;
        font-family: var(--font-content);
        background-color: #409EFF;
        border-color: #409EFF;
        border-radius: 4px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(64, 158, 255, 0.4);
          background-color: #66b1ff;
        }
        
        &:active {
          transform: translateY(0);
          box-shadow: 0 2px 8px rgba(64, 158, 255, 0.4);
        }
      }
    }
  }

  .login-footer {
    text-align: center;
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 20px;
    color: rgba(255, 255, 255, 0.6);
    background: linear-gradient(to top, rgba(10, 31, 61, 0.8), transparent);
    backdrop-filter: blur(5px);
    z-index: 1;
    
    p {
      margin: 10px 0;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
    
    a {
      padding: 0 5px;
      color: rgba(255, 255, 255, 0.8);
      text-decoration: none;
      transition: all 0.3s;
      
      &:focus,
      &:hover {
        color: #409EFF;
      }
    }
  }

  &-left,
  &-right {
    position: relative;
    min-height: 500px;
    align-items: center;
    display: flex;
  }

  /* 装饰圆圈 */
  .decoration-circle {
    position: absolute;
    border-radius: 50%;
    z-index: -1;
    opacity: 0.5;
    filter: blur(40px);
  }
  
  .decoration-circle-1 {
    width: 200px;
    height: 200px;
    top: 15%;
    left: 10%;
    background: radial-gradient(circle, rgba(56, 174, 243, 0.3), rgba(10, 31, 61, 0.1));
    animation: float 8s ease-in-out infinite;
  }
  
  .decoration-circle-2 {
    width: 300px;
    height: 300px;
    bottom: 10%;
    right: 5%;
    background: radial-gradient(circle, rgba(56, 174, 243, 0.25), rgba(10, 31, 61, 0.1));
    animation: float 12s ease-in-out infinite reverse;
  }
  
  .decoration-circle-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    right: 25%;
    background: radial-gradient(circle, rgba(56, 174, 243, 0.2), rgba(10, 31, 61, 0.1));
    animation: float 10s ease-in-out 2s infinite;
  }
}

/* 科技元素动画 */
@keyframes float {
  0% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(-20px) translateX(10px);
  }
  100% {
    transform: translateY(0) translateX(0);
  }
}

@keyframes grid-move {
  0% {
    transform: perspective(500px) rotateX(60deg) translateY(0);
  }
  100% {
    transform: perspective(500px) rotateX(60deg) translateY(50px);
  }
}

@keyframes scan-move {
  0% {
    transform: translateY(-50vh);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: translateY(150vh);
    opacity: 0;
  }
}

@keyframes title-glow {
  0% {
    text-shadow: 0 0 5px rgba(56, 174, 243, 0.3), 0 0 10px rgba(56, 174, 243, 0);
  }
  100% {
    text-shadow: 0 0 10px rgba(56, 174, 243, 0.5), 0 0 20px rgba(56, 174, 243, 0.3);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.4;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes draw {
  0% {
    stroke-dashoffset: 100;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes blink {
  0%, 100% {
    opacity: 0.3;
    box-shadow: 0 0 3px var(--tech-glow);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 8px var(--tech-glow), 0 0 12px var(--tech-glow);
  }
}

@keyframes glow {
  from {
    stroke: #38AEF3;
    filter: drop-shadow(0 0 2px rgba(56, 174, 243, 0.3));
  }
  to {
    stroke: #38AEF3;
    filter: drop-shadow(0 0 8px rgba(56, 174, 243, 0.7));
  }
}

@keyframes radar-scan {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes data-grow {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes shine {
  to {
    background-position: 200% center;
  }
}
</style>
