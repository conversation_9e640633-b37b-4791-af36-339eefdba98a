<template>
  <div class="patrol-dashboard">
    <!-- 顶部状态卡片区域 -->
    <div class="status-cards">
      <el-card class="status-card" shadow="never">
        <div class="card-title">在线车辆</div>
        <div class="card-value">
          {{ monitoringData.onlineVehicles }}/{{ monitoringData.totalVehicles }}
        </div>
        <div class="completion-rate">
          <span>在线率</span>
          <el-progress
            :percentage="monitoringData.completionRate"
            :show-text="false"
            :stroke-width="5"
            class="progress-bar"
          />
          <span>{{ monitoringData.completionRate }}%</span>
        </div>
      </el-card>

      <el-card class="status-card" shadow="never">
        <div class="card-title">今日里程</div>
        <div class="card-value">
          {{ monitoringData.todayDistance }} <span class="unit">km</span>
        </div>
        <div class="stat-trend increasing">
          <el-icon>
            <TopRight />
          </el-icon>
          <span>较昨日增长8.5%</span>
        </div>
      </el-card>

      <el-card class="status-card warning-card" shadow="never">
        <div class="card-title">告警提示</div>
        <div class="warning-message">
          <el-icon>
            <Warning />
          </el-icon>
          <span class="red-text">{{ monitoringData.warningMessage }}</span>
        </div>
        <div class="warning-actions">
          <el-link type="primary" class="action-link">暂时忽略</el-link>
          <el-link type="primary" class="action-link">处理</el-link>
        </div>
      </el-card>

      <el-card class="status-card" shadow="never">
        <div class="card-title">任务完成率</div>
        <div class="card-value">{{ monitoringData.taskCompletionRate }}%</div>
        <el-progress
          :percentage="monitoringData.taskCompletionRate"
          :show-text="false"
          :stroke-width="8"
          class="large-progress"
          :color="['#1890ff', '#52c41a']"
        />
      </el-card>
    </div>

    <el-input v-model="searchQuery" placeholder="搜索车辆编号/车牌号" class="search-bar">
      <template #prefix>
        <el-icon>
          <Search />
        </el-icon>
      </template>
    </el-input>

    <!-- 中间主区域 -->
    <div class="content-layout">
      <!-- 左侧地图区域 -->
      <el-card class="map-container" shadow="never">
        <!-- <div id="container"></div> -->
        <div id="container" class="patrol-map"></div>
      </el-card>

      <!-- 右侧监控区域 -->
      <div class="monitor-area">
        <el-card
          v-for="(camera, index) in 3"
          :key="index"
          class="monitor-card"
          shadow="never"
        >
          <template #header>
            <div class="monitor-header">
              <span>实时监控</span>
              <el-dropdown
                trigger="click"
                @command="handleCameraChange.bind(null, `camera${index + 1}`)"
              >
                <span class="camera-select">
                  {{ getCameraLabel(selectedCameras[`camera${index + 1}`]) }}
                  <el-icon><ArrowDown /></el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="option in cameraOptions"
                      :key="option.value"
                      :command="option.value"
                    >
                      {{ option.label }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>

          <div class="monitor-screen">
            <!-- 使用新的 MpegtsPlayer 组件 -->
            <div
              :class="[
                'camera-feed',
                index === 2 ? 'type-3' : index === 1 ? 'type-2' : '',
              ]"
            >
              <MpegtsPlayer
                :stream-url="
                  getCameraStreamUrl(index, selectedCameras[`camera${index + 1}`])
                "
                :camera-info="
                  index === 2
                    ? '区域: 商业中心'
                    : `车牌: 京${String.fromCharCode(65 + index)}-2025`
                "
                :camera-type="index === 2 ? 'type-3' : index === 1 ? 'type-2' : 'default'"
                :autoplay="true"
                @error="handlePlayerError(index, $event)"
                @status-change="handlePlayerStatusChange(index, $event)"
              />
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 底部车辆信息区域 -->
    <div class="vehicle-info-area">
      <el-card class="vehicle-carousel" shadow="never">
        <!-- 使用 el-carousel 组件替换自定义轮播 -->
        <el-carousel
          :interval="8000"
          arrow="always"
          indicator-position="outside"
          height="180px"
        >
          <!-- 为每页创建一个 el-carousel-item -->
          <el-carousel-item
            v-for="page in Math.ceil(vehiclesData.length / itemsPerPage)"
            :key="page"
          >
            <div class="vehicle-page">
              <!-- 在每个轮播项中显示多个车辆卡片 -->
              <div
                v-for="vehicle in getPageVehicles(page - 1)"
                :key="vehicle.id"
                class="vehicle-card"
              >
                <div class="vehicle-header">
                  <div class="vehicle-title">巡逻车 {{ vehicle.id }}</div>
                  <el-tag
                    :type="getStatusType(vehicle.status)"
                    class="vehicle-status"
                    size="small"
                    :style="getStatusStyle(vehicle.status)"
                  >
                    <template v-if="vehicle.status === 'online'">在线</template>
                    <template v-else-if="vehicle.status === 'offline'">离线</template>
                    <template v-else-if="vehicle.status === 'charging'">
                      <span class="charging-icon">⚡</span>充电
                    </template>
                  </el-tag>
                </div>

                <div class="vehicle-location">
                  <el-icon>
                    <Location />
                  </el-icon>
                  <span>{{ vehicle.location }}</span>
                </div>

                <div class="vehicle-details">
                  <div class="detail-item">
                    <div class="detail-label">电量</div>
                    <div class="detail-value">{{ vehicle.battery }}%</div>
                    <el-progress
                      :percentage="vehicle.battery"
                      :show-text="false"
                      :stroke-width="4"
                      :color="getBatteryColor(vehicle.battery)"
                      class="battery-bar"
                    />
                  </div>
                  <div class="detail-item">
                    <div class="detail-label">速度</div>
                    <div class="detail-value">{{ vehicle.speed }} km/h</div>
                  </div>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </el-card>

      <!-- 任务列表 -->
      <el-card class="task-list-card" shadow="never">
        <el-table
          :data="tasksData"
          style="width: 100%"
          :header-cell-style="{
            background: 'rgba(0, 0, 0, 0.3)',
            color: 'rgba(255, 255, 255, 0.5)',
          }"
          :row-style="{ borderBottom: '1px solid rgba(24, 144, 255, 0.2)' }"
          :cell-style="{ color: 'white' }"
          :stripe="false"
          :border="false"
        >
          <el-table-column prop="time" label="时间" min-width="110"></el-table-column>
          <el-table-column prop="location" label="地点" min-width="120"></el-table-column>
          <el-table-column prop="type" label="类型" min-width="100"></el-table-column>
          <el-table-column label="状态" min-width="90">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 'processing' ? 'primary' : 'success'"
                size="small"
                effect="plain"
              >
                {{ scope.row.status === "processing" ? "处理中" : "已完成" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="handler" label="处理人" min-width="90"></el-table-column>
          <el-table-column label="操作" min-width="90" align="center">
            <template #default>
              <el-link type="primary" size="small">查看详情</el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
// 为AMap声明全局类型
declare global {
  interface Window {
    AMap: any; // 使用any类型避免与amap.d.ts的类型冲突
    _AMapSecurityConfig: {
      securityJsCode: string;
    };
    showVehicleDetails: (id: string) => void;
    trackVehicle: (id: string) => void;
    retryLoadMap: () => void;
  }
}

// 扩展高德地图Marker类型
interface ExtendedMarker {
  textMarker?: any;
  getExtData: () => any;
  setExtData: (data: any) => void;
  isInGeofence?: boolean;
  on: (event: string, callback: (...args: any[]) => void) => void;
  moveTo: (position: any, options: any) => void;
  getPosition: () => any;
}

import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick } from "vue";
// 导入Element Plus的图标
import {
  TopRight,
  Warning,
  Search,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  VideoCamera,
  Location,
  Refresh,
} from "@element-plus/icons-vue";
// 导入echarts
import * as echarts from "echarts/core";
import { PieChart } from "echarts/charts";
import { TitleComponent, TooltipComponent, LegendComponent } from "echarts/components";
import { LabelLayout } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
// 导入AMapLoader
import AMapLoader from "@amap/amap-jsapi-loader";
// 导入车辆服务
import vehicleService, { VehicleRealtimeStatus } from "@/services/vehicleService";
import { ElNotification } from "element-plus";
// 导入mpegts.js库
import mpegts from "mpegts.js";
import MpegtsPlayer from "@/components/MpegtsPlayer.vue";

// 注册必要的组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  LabelLayout,
  CanvasRenderer,
]);

// 当前时间
const currentTime = ref<string>("00:00:00");

// 更新当前时间
const updateCurrentTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");
  currentTime.value = `${hours}:${minutes}:${seconds}`;
};

// 每秒更新时间
const timeInterval = setInterval(updateCurrentTime, 1000);
updateCurrentTime(); // 初始化时间

// 声明变量
let map: any = null;
let traffic: any = null;
let toolbar: any = null;
let markers: any[] = [];
let carPath: any = null;
let movingMarker: any = null;
const carouselInterval = ref<number | null>(null);
const cameraFeedInterval = ref<number | null>(null);
const taskScrollTimer = ref<number | null>(null);
let taskChart: any = null;
let vehicleMarkers: Map<string, any> = new Map(); // 存储车辆标记的Map，设为any类型
let carMarker: any = null; // 车辆标记

// 跟踪车辆最后通信时间
const lastVehicleCommunication = new Map<string, number>();

// 通信检查定时器
let communicationCheckTimer: number | null = null;

// 更新地图上的车辆标记
const updateVehicleOnMap = (vehicleStatus: VehicleRealtimeStatus) => {
  if (!map) return;

  const {
    deviceName,
    licensePlate,
    lon,
    lat,
    batteryLevel,
    currentSpeed,
    chargingStatus,
  } = vehicleStatus;

  const position = new window.AMap.LngLat(lon, lat);

  // 检查该车辆是否已有标记
  if (vehicleMarkers.has(deviceName)) {
    // 更新现有标记位置
    const marker = vehicleMarkers.get(deviceName);

    // 使用动画平滑移动到新位置
    marker.moveTo(position, {
      duration: 1000, // 1秒内完成移动
      autoRotation: true, // 自动旋转
    });

    // 更新marker的内部数据
    marker.setExtData({
      ...marker.getExtData(),
      batteryLevel,
      currentSpeed,
      chargingStatus,
    });

    // 更新文本标记位置
    if (marker.textMarker) {
      marker.textMarker.setPosition(position);
    }
  } else {
    // 创建新的车辆标记
    const marker = new window.AMap.Marker({
      position: position,
      icon: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",
      offset: new window.AMap.Pixel(-13, -26),
      title: licensePlate,
      extData: {
        deviceName,
        licensePlate,
        batteryLevel,
        currentSpeed,
        chargingStatus,
      },
    });

    // 创建一个文本标记显示车牌号
    try {
      const textMarker = new window.AMap.Text({
        text: licensePlate,
        position: position,
        offset: new window.AMap.Pixel(0, -45),
        style: {
          "background-color": "#1890ff",
          "border-width": "0",
          "text-align": "center",
          "font-size": "12px",
          color: "white",
          padding: "2px 6px",
          "border-radius": "2px",
          "font-weight": "bold",
        },
      });

      // 将文本标记添加到地图
      map.add(textMarker);

      // 将文本标记存储到车辆标记中，便于后续更新
      marker.textMarker = textMarker;
    } catch (error) {
      console.error("创建文本标记失败:", error);
    }

    // 添加点击事件
    marker.on("click", () => {
      const extData = marker.getExtData();
      console.log(extData);

      // 使用辅助函数显示信息窗口
      showVehicleInfoWindow(extData, marker);
    });

    // 将标记添加到地图
    map.add(marker);

    // 存储到Map中
    vehicleMarkers.set(deviceName, marker);

    // 显示新车辆通知
    ElNotification({
      title: "新车辆加入",
      message: `${licensePlate} (${deviceName}) 已连接到系统`,
      type: "success",
      duration: 3000,
    });
  }
};

// 更新车辆卡片信息
const updateVehicleCard = (vehicleStatus: VehicleRealtimeStatus) => {
  const {
    deviceName,
    licensePlate,
    batteryLevel,
    currentSpeed,
    chargingStatus,
    lon,
    lat,
    location,
  } = vehicleStatus;

  // 查找并更新车辆数据
  const vehicleIndex = vehiclesData.findIndex(
    (v) => v.id === deviceName || v.id === licensePlate
  );

  if (vehicleIndex !== -1) {
    // 更新现有车辆数据
    vehiclesData[vehicleIndex].battery = batteryLevel;
    vehiclesData[vehicleIndex].speed = currentSpeed;
    vehiclesData[vehicleIndex].status = chargingStatus ? "charging" : "online"; // 确保状态更新为在线
    vehiclesData[vehicleIndex].location = location;
  } else {
    // 添加新车辆
    vehiclesData.push({
      id: deviceName,
      status: chargingStatus ? "charging" : "online",
      battery: batteryLevel,
      speed: currentSpeed,
      location: location,
    });

    // 为新添加的车辆初始化通信时间
    lastVehicleCommunication.set(deviceName, Date.now());
  }
};

// 处理车辆状态更新
const handleVehicleUpdate = (vehicleStatus: VehicleRealtimeStatus) => {
  const { deviceName } = vehicleStatus;

  // 记录最后通信时间
  lastVehicleCommunication.set(deviceName, Date.now());

  // 检查是否从离线状态恢复
  const vehicleIndex = vehiclesData.findIndex((v) => v.id === deviceName);
  if (vehicleIndex !== -1 && vehiclesData[vehicleIndex].status === "offline") {
    // 车辆从离线状态恢复
    ElNotification({
      title: "车辆通信已恢复",
      message: `车辆 ${deviceName} 已恢复在线状态`,
      type: "success",
      duration: 3000,
    });

    // 恢复地图标记的颜色
    const marker = vehicleMarkers.get(deviceName);
    if (marker) {
      // 恢复标记的原始图标（不使用灰色滤镜）
      marker.setIcon(
        new window.AMap.Icon({
          image: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",
          size: new window.AMap.Size(26, 52),
          imageSize: new window.AMap.Size(26, 52),
          imageOffset: new window.AMap.Pixel(0, 0),
          // 不使用灰色滤镜
        })
      );

      // 如果有文本标记，恢复为蓝色
      if (marker.textMarker) {
        marker.textMarker.setStyle({
          "background-color": "#1890ff",
          "border-width": "0",
          "text-align": "center",
          "font-size": "12px",
          color: "white",
          padding: "2px 6px",
          "border-radius": "2px",
          "font-weight": "bold",
        });
      }
    }
  }

  // 更新地图上的车辆位置
  updateVehicleOnMap(vehicleStatus);

  // 更新车辆信息卡片
  updateVehicleCard(vehicleStatus);

  // 更新监控数据统计
  updateMonitoringStats();
};

// 更新监控统计数据
const updateMonitoringStats = () => {
  // 更新在线车辆数
  monitoringData.onlineVehicles = vehiclesData.filter(
    (v) => v.status !== "offline"
  ).length;

  // 更新完成率（根据在线车辆与总车辆比例）
  monitoringData.completionRate = Math.round(
    (monitoringData.onlineVehicles / monitoringData.totalVehicles) * 100
  );
};

// 初始化地图
const initMap = async () => {
  window._AMapSecurityConfig = {
    securityJsCode: "a9676a96467adef5269a629f780bdb07",
  };

  try {
    AMapLoader.load({
      key: "eea9447f9cd0ad60cd7a7b612974f3fa", // Your API key
      version: "2.0",
      plugins: ["AMap.Scale", "AMap.MouseTool", "AMap.MoveAnimation", "AMap.ToolBar"],
    })
      .then((AMap) => {
        try {
          // 创建地图实例
          map = new AMap.Map("container", {
            viewMode: "3D",
            zoom: 15,
            center: [104.002052, 30.713956],
            mapStyle: "amap://styles/darkblue", // 暗黑主题
          });

          // 初始化标记组
          markers = [];

          // 添加初始车辆标记
          addVehicleMarkers(map, AMap);

          // 初始化实时交通图层
          traffic = new AMap.TileLayer.Traffic({
            zIndex: 10,
          });

          // 添加地图控制面板（包含标准控件）
          addMapControlPanel(map, AMap);

          // 在地图初始化成功后连接WebSocket
          connectVehicleService();
        } catch (error) {
          console.error("地图实例创建失败:", error);
          showSimulatedMap(document.getElementById("container"));
        }
      })
      .catch((e) => {
        console.error("地图加载失败:", e);
        const container = document.getElementById("container");
        if (container) {
          showSimulatedMap(container);
        }
      });
  } catch (error) {
    console.error("初始化地图过程中出错:", error);
    showSimulatedMap(document.getElementById("container"));
  }
};

// 连接车辆服务
const connectVehicleService = async () => {
  try {
    // 设置车辆更新回调
    vehicleService.onVehicleUpdate(handleVehicleUpdate);

    // 连接WebSocket
    await vehicleService.connect();

    // 连接成功通知
    ElNotification({
      title: "连接成功",
      message: "已成功连接到车辆实时数据服务",
      type: "success",
      duration: 3000,
    });
  } catch (error) {
    console.error("连接车辆服务失败:", error);

    // 显示错误通知，3秒后自动消失
    ElNotification({
      title: "连接失败",
      message: "无法连接到车辆实时数据服务",
      type: "warning",
      duration: 3000, // 3秒后自动消失
    });

    // 在5秒后自动尝试重新连接
    setTimeout(() => {
      if (!vehicleService.isConnected()) {
        console.log("自动重试连接...");
        connectVehicleService();
      }
    }, 5000);
  }
};

// 添加初始车辆标记
const addVehicleMarkers = (map: any, AMap: any) => {
  if (!map) return;

  // 清除现有标记
  if (markers.length > 0) {
    map.remove(markers);
    markers = [];
  }

  // 不再预先添加模拟车辆，而是等待WebSocket连接后动态添加
  console.log("地图已准备就绪，等待实时车辆数据...");
};

// 定义播放器对象数组
const mpegtsPlayers = ref<any[]>([]);

// 记录视频连接状态
const videoStreamStatus = reactive({
  0: "loading",
  1: "loading",
  2: "loading",
});

// 获取摄像头流URL
const getCameraStreamUrl = (index: number, cameraType: string): string => {
  // 基础URL，使用端口8080
  let streamUrl = `http://192.168.1.130:8090/live/livestream0.flv`;

  // 根据摄像头类型附加不同的后缀
  // if (cameraType === 'front') {
  //   streamUrl += '_front.ts';
  // } else if (cameraType === 'back') {
  //   streamUrl += '_back.ts';
  // } else if (cameraType === 'left') {
  //   streamUrl += '_left.ts';
  // } else if (cameraType === 'right') {
  //   streamUrl += '_right.ts';
  // } else if (cameraType === '360') {
  //   streamUrl += '_360.ts';
  // } else {
  //   streamUrl += '.ts';
  // }

  return streamUrl;
};

// 处理播放器错误
const handlePlayerError = (index: number, error: any) => {
  console.error(`播放器${index}错误:`, error);
  videoStreamStatus[index as 0 | 1 | 2] = "error";

  ElNotification({
    title: "视频流连接失败",
    message: `摄像头 ${getCameraLabel(
      selectedCameras[`camera${index + 1}`]
    )} 连接失败，请检查网络或重试`,
    type: "error",
    duration: 3000,
  });
};

// 处理播放器状态变化
const handlePlayerStatusChange = (index: number, status: string) => {
  console.log(`播放器${index}状态变更:`, status);
  videoStreamStatus[index as 0 | 1 | 2] = status;

  // if (status === 'playing') {
  //   ElNotification({
  //     title: '视频流已连接',
  //     message: `摄像头 ${getCameraLabel(selectedCameras[`camera${index + 1}`])} 已成功连接`,
  //     type: 'success',
  //     duration: 2000
  //   });
  // }
};

// 处理摄像头选择变更
const handleCameraChange = (cameraId: string, value: string) => {
  const oldValue = selectedCameras[cameraId];
  selectedCameras[cameraId] = value;

  // 获取摄像头索引
  const index = parseInt(cameraId.replace("camera", "")) - 1;

  // 更新状态
  videoStreamStatus[index as 0 | 1 | 2] = "loading";

  // 通知用户切换摄像头
  ElNotification({
    title: "切换摄像头",
    message: `正在切换至${getCameraLabel(value)}`,
    type: "info",
    duration: 2000,
  });
};

// 初始化视频播放器 - 不再需要，由组件自动处理
const initVideoPlayers = () => {
  // 检查浏览器是否支持 mpegts.js
  if (!mpegts.isSupported()) {
    ElNotification({
      title: "不支持的浏览器",
      message: "您的浏览器不支持MPEG-TS视频播放，请使用Chrome、Firefox或Edge等现代浏览器",
      type: "error",
      duration: 5000,
    });
  }
};

// 销毁所有播放器 - 不再需要，由组件自动处理
const destroyVideoPlayers = () => {
  // 组件会自动处理清理
  mpegtsPlayers.value = [];
};

// 组件挂载完成后初始化
onMounted(() => {
  initMap();
  startAutoCarousel();

  // 不再需要手动初始化播放器
  // nextTick(() => {
  //   initVideoPlayers();
  // });

  // 初始化车辆通信检查定时器
  communicationCheckTimer = window.setInterval(checkVehicleCommunication, 2000);

  // 初始化图表
  nextTick(() => {
    initTaskChart();
  });

  // 初始化任务列表滚动
  nextTick(() => {
    setTimeout(() => {
      initTaskScroll();
    }, 1000);
  });
});

// 组件卸载时清除定时器和连接
onBeforeUnmount(() => {
  // 清除时间更新定时器
  clearInterval(timeInterval);

  // 清除轮播定时器
  if (carouselInterval.value) {
    clearInterval(carouselInterval.value);
  }

  // 清除摄像头滚动定时器
  if (cameraFeedInterval.value) {
    clearInterval(cameraFeedInterval.value);
  }

  // 清除任务滚动定时器
  if (taskScrollTimer.value) {
    clearInterval(taskScrollTimer.value);
  }

  // 销毁ECharts实例
  if (taskChart) {
    taskChart.dispose();
  }

  // 断开WebSocket连接
  vehicleService.disconnect();

  // 清除通信检查定时器
  if (communicationCheckTimer !== null) {
    clearInterval(communicationCheckTimer);
    communicationCheckTimer = null;
  }

  // 不再需要手动销毁播放器
  // destroyVideoPlayers();
});

// 清除地图上的所有标记
const clearMapMarkers = () => {
  if (map && markers.length) {
    // 清除车辆标记
    map.remove(markers);
    markers = [];
  }

  // 清除车辆标记和关联的文本标记
  if (vehicleMarkers.size > 0) {
    // 先删除所有文本标记
    vehicleMarkers.forEach((marker) => {
      if (marker.textMarker) {
        map.remove(marker.textMarker);
      }
    });

    // 再删除所有车辆标记
    const vehicleMarkersArray = Array.from(vehicleMarkers.values());
    map.remove(vehicleMarkersArray);

    // 清空Map
    vehicleMarkers.clear();
  }
};

// 打开车辆信息窗口
// const openVehicleInfoWindow = (map: any, marker: any, vehicle: any, AMap: any) => {
//   const content = `
//     <div class="amap-info-window">
//       <div class="info-title">${vehicle.id}</div>
//       <div class="info-content">
//         <div>状态: ${vehicle.status === "online" ? "在线" : "离线"}</div>
//         <div>电量: ${vehicle.battery}%</div>
//         <div>速度: ${vehicle.speed} km/h</div>
//         <div>位置: ${vehicle.location}</div>
//       </div>
//       <div class="info-actions">
//         <button class="info-btn" onclick="window.showVehicleDetails('${vehicle.id}')">查看详情</button>
//         <button class="info-btn" onclick="window.trackVehicle('${vehicle.id}')">开始跟踪</button>
//       </div>
//     </div>
//   `;

//   const infoWindow = new AMap.InfoWindow({
//     content: content,
//     offset: new AMap.Pixel(0, -32)
//   });

//   infoWindow.open(map, marker.getPosition());

//   // 将showVehicleDetails和trackVehicle函数添加到全局window对象
//   window.showVehicleDetails = (id: string) => {
//     alert(`查看车辆${id}详情`);
//     infoWindow.close();
//   };

//   window.trackVehicle = (id: string) => {
//     alert(`开始跟踪车辆${id}`);
//     infoWindow.close();

//     // 如果地图支持移动动画，可以添加路径跟踪演示
//     if (AMap.MoveAnimation && markers.length > 0) {
//       const targetMarker = markers.find(m => m.getTitle() === `巡逻车 ${id}`);
//       if (targetMarker) {
//         // 生成一条模拟路径
//         const startPoint = targetMarker.getPosition();
//         const pathPoints = [
//           startPoint,
//           new AMap.LngLat(startPoint.getLng() + 0.01, startPoint.getLat() + 0.005),
//           new AMap.LngLat(startPoint.getLng() + 0.015, startPoint.getLat() - 0.005),
//           new AMap.LngLat(startPoint.getLng() + 0.02, startPoint.getLat() + 0.01)
//         ];

//         // 添加路径线
//         if (carPath) {
//           map.remove(carPath);
//         }

//         carPath = new AMap.Polyline({
//           path: pathPoints,
//           strokeColor: '#28F',
//           strokeWeight: 6,
//           strokeOpacity: 0.8
//         });
//         map.add(carPath);

//         // 移动标记
//         targetMarker.moveAlong(pathPoints, {
//           duration: 10000,
//           autoRotation: true
//         });
//       }
//     }
//   };
// };

// 显示模拟地图 (当高德地图加载失败时)
const showSimulatedMap = (container: HTMLElement | null) => {
  if (!container) {
    console.error("无法获取地图容器元素");
    return;
  }

  container.innerHTML = `
    <div class="map-grid"></div>
    <div class="map-glow"></div>
    <div class="patrol-vehicles">
      <div class="vehicle vehicle-1"></div>
      <div class="vehicle vehicle-2"></div>
      <div class="vehicle vehicle-3"></div>
      <div class="vehicle vehicle-4"></div>
      <div class="vehicle vehicle-5"></div>
      <div class="vehicle vehicle-6"></div>
      <div class="vehicle vehicle-7"></div>
      <div class="vehicle vehicle-8"></div>
      <div class="vehicle vehicle-9"></div>
      <div class="vehicle vehicle-ambulance"></div>
    </div>
    <div class="map-error-overlay">
      <div class="error-message">地图加载失败，请检查网络连接或刷新页面重试</div>
      <button class="retry-button">重试</button>
    </div>
  `;

  // 添加重试按钮事件
  const retryButton = container.querySelector(".retry-button");
  if (retryButton) {
    retryButton.addEventListener("click", () => {
      window.retryLoadMap = initMap;
      initMap();
    });
  }
};

// 搜索查询
const searchQuery = ref("");

// 监控数据（初始值，将通过实时数据更新）
const monitoringData = reactive({
  onlineVehicles: 0,
  totalVehicles: 5,
  completionRate: 20,
  todayDistance: 10,
  taskCompletionRate: 20,
  warningMessage: "等待连接气象服务...",
});

// 摄像头列表数据
const cameraOptions = reactive([
  { value: "front", label: "前置摄像头" },
  { value: "back", label: "后置摄像头" },
  { value: "left", label: "左侧摄像头" },
  { value: "right", label: "右侧摄像头" },
  { value: "360", label: "360°全景摄像头" },
]);

// 根据摄像头值获取标签
const getCameraLabel = (value: string) => {
  const camera = cameraOptions.find((c) => c.value === value);
  return camera ? camera.label : "未知摄像头";
};

// 当前选中的摄像头
const selectedCameras = reactive({
  camera1: "front",
  camera2: "back",
  camera3: "360",
}) as { [key: string]: string };

// 轮播相关状态
const currentPage = ref(0);
const itemsPerPage = 4; // 每页显示的车辆数
const vehicleCarousel = ref<HTMLElement | null>(null);

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(vehiclesData.length / itemsPerPage);
});

// 计算最大页码（从0开始）
const maxPage = computed(() => {
  return totalPages.value - 1;
});

// 翻到上一页
const prevPage = () => {
  if (currentPage.value > 0) {
    currentPage.value--;
  }
};

// 翻到下一页
const nextPage = () => {
  if (currentPage.value < maxPage.value) {
    currentPage.value++;
  }
};

// 跳转到指定页
const goToPage = (page: number) => {
  if (page >= 0 && page <= maxPage.value) {
    currentPage.value = page;
  }
};

// 自动轮播
const startAutoCarousel = () => {
  carouselInterval.value = setInterval(() => {
    if (currentPage.value < maxPage.value) {
      currentPage.value++;
    } else {
      currentPage.value = 0;
    }
  }, 8000); // 每8秒翻页一次
};

// 根据状态获取Tag类型
const getStatusType = (status: string) => {
  switch (status) {
    case "online":
      return "success";
    case "offline":
      return "info"; // 使用info类型，但会被自定义样式覆盖
    case "charging":
      return "warning";
    default:
      return "";
  }
};

// 获取车辆状态的额外样式
const getStatusStyle = (status: string) => {
  if (status === "offline") {
    return {
      backgroundColor: "#909399",
      color: "#f5f5f5",
      border: "1px solid #f56c6c",
    };
  }
  return {};
};

// 电池颜色
const getBatteryColor = (level: number) => {
  if (level <= 20) return "#ff4d4f";
  if (level <= 50) return "#faad14";
  return "#52c41a";
};

// 车辆数据 - 将从后端实时获取
const vehiclesData = reactive<
  Array<{
    id: string;
    status: string;
    battery: number;
    speed: number;
    location: string;
  }>
>([]);

// 任务列表数据
const tasksData = reactive([
  {
    time: "08:30:25",
    location: "武侯区人民南路",
    type: "例行巡逻",
    status: "completed",
    handler: "王小明",
  },
  {
    time: "09:45:12",
    location: "锦江区春熙路",
    type: "设备维护",
    status: "processing",
    handler: "李小红",
  },
  {
    time: "10:15:36",
    location: "青羊区天府广场",
    type: "安全检查",
    status: "processing",
    handler: "张小龙",
  },
  {
    time: "11:30:20",
    location: "高新区天府三街",
    type: "例行巡逻",
    status: "completed",
    handler: "刘小华",
  },
  {
    time: "13:45:10",
    location: "金牛区万达广场",
    type: "应急响应",
    status: "processing",
    handler: "赵小勇",
  },
  {
    time: "15:20:45",
    location: "成华区建设路",
    type: "安全检查",
    status: "completed",
    handler: "孙小丽",
  },
]);

// 模拟数据变化
const simulateDataChanges = () => {
  // 随机更新数据
  const dataInterval = setInterval(() => {
    // 更新完成率
    monitoringData.completionRate = Math.min(
      100,
      monitoringData.completionRate + (Math.random() > 0.7 ? 1 : 0)
    );

    // 更新里程数
    monitoringData.todayDistance += Math.floor(Math.random() * 5);

    // 更新任务完成率
    monitoringData.taskCompletionRate = Math.min(
      100,
      parseFloat((monitoringData.taskCompletionRate + Math.random() * 0.1).toFixed(1))
    );

    // 随机增减在线车辆
    if (Math.random() > 0.8) {
      monitoringData.onlineVehicles = Math.min(
        monitoringData.totalVehicles,
        Math.max(monitoringData.onlineVehicles + (Math.random() > 0.5 ? 1 : -1), 0)
      );
    }
  }, 3000); // 每3秒更新一次

  // 在组件卸载前清除定时器
  onBeforeUnmount(() => {
    clearInterval(dataInterval);
  });
};

// 模拟车辆数据变化
const simulateVehicleDataChanges = () => {
  const vehicleInterval = setInterval(() => {
    vehiclesData.forEach((vehicle) => {
      if (vehicle.status === "online") {
        // 随机更新速度
        vehicle.speed = Math.max(
          0,
          vehicle.speed + (Math.random() > 0.5 ? 1 : -1) * Math.floor(Math.random() * 3)
        );

        // 随机消耗电量
        if (Math.random() > 0.7) {
          vehicle.battery = Math.max(0, vehicle.battery - 1);

          // 如果电量过低，状态改为充电
          if (vehicle.battery < 10 && Math.random() > 0.7) {
            vehicle.status = "charging";
            vehicle.speed = 0;
          }
        }
      } else if (vehicle.status === "charging") {
        // 充电中，增加电量
        vehicle.battery = Math.min(100, vehicle.battery + 1);

        // 如果充满电，状态改为在线
        if (vehicle.battery > 90 && Math.random() > 0.7) {
          vehicle.status = "online";
        }
      } else if (vehicle.status === "offline" && Math.random() > 0.9) {
        // 有低概率从离线恢复在线
        vehicle.status = "online";
        vehicle.battery = 50 + Math.floor(Math.random() * 50);
      }
    });
  }, 5000); // 每5秒更新一次

  // 在组件卸载前清除定时器
  onBeforeUnmount(() => {
    clearInterval(vehicleInterval);
  });
};

// 模拟告警信息变化
const simulateAlerts = () => {
  const alertMessages = [
    "车辆故障可能影响巡检正常运行，请尽快提入系统中心处理",
    "武侯区人民南路附近发现可疑人员活动，请安排人员前往核查",
    "巡逻车B-2026电量过低，请安排及时充电",
    "锦江区春熙路监控点2号摄像头离线，请检查网络连接",
    "今日巡逻覆盖率未达标，请调整巡逻路线",
    "高新区天府软件园有交通拥堵情况，请提醒车辆绕行",
  ];

  // 随机更换告警消息
  const alertInterval = setInterval(() => {
    const randomIndex = Math.floor(Math.random() * alertMessages.length);
    monitoringData.warningMessage = alertMessages[randomIndex];
  }, 15000); // 每15秒更换一次告警消息

  // 在组件卸载前清除定时器
  onBeforeUnmount(() => {
    clearInterval(alertInterval);
  });
};

// 动态更新摄像头画面
const updateCameraFeeds = () => {
  // 模拟摄像头画面刷新
  cameraFeedInterval.value = setInterval(() => {
    // 这里只是通过样式变化来模拟摄像头画面更新
    const feeds = document.querySelectorAll(".camera-feed");
    feeds.forEach((feed) => {
      const opacity = 0.6 + Math.random() * 0.4;
      (feed as HTMLElement).style.opacity = opacity.toString();

      // 随机微调摄像头亮度和对比度，模拟真实摄像头效果
      const brightness = 90 + Math.floor(Math.random() * 20);
      const contrast = 95 + Math.floor(Math.random() * 10);
      (feed as HTMLElement).style.filter = `brightness(${brightness}%) contrast(${contrast}%)`;
    });
  }, 2000); // 每2秒更新一次
};

// 初始化任务列表滚动
const initTaskScroll = () => {
  const taskListEl = document.querySelector(".task-list-card .el-table__body-wrapper");

  if (!taskListEl) {
    // 如果元素还没有渲染，等待下一次DOM更新后重试
    nextTick(() => {
      setTimeout(() => {
        initTaskScroll();
      }, 500);
    });
    return; // 这里的return是在函数内部，没有问题
  }

  const startTaskScroll = () => {
    // 只有当内容高度超过容器高度时才滚动
    if (taskListEl && taskListEl.scrollHeight > taskListEl.clientHeight) {
      taskScrollTimer.value = setInterval(() => {
        // 确保taskListEl存在
        if (taskListEl) {
          // 如果已经滚动到底部，重新开始
          if (taskListEl.scrollTop >= taskListEl.scrollHeight - taskListEl.clientHeight) {
            // 平滑回到顶部
            taskListEl.scrollTo({
              top: 0,
              behavior: "smooth",
            });
          } else {
            // 每次滚动一点
            taskListEl.scrollBy({
              top: 1,
              behavior: "smooth",
            });
          }
        }
      }, 50); // 调整速度，数值越小滚动越快
    }
  };

  // 确保taskListEl存在再添加事件监听器
  if (taskListEl) {
    // 用户交互时暂停滚动
    taskListEl.addEventListener("mouseenter", () => {
      if (taskScrollTimer.value) {
        clearInterval(taskScrollTimer.value);
        taskScrollTimer.value = null;
      }
    });

    // 用户离开时恢复滚动
    taskListEl.addEventListener("mouseleave", () => {
      if (!taskScrollTimer.value) {
        startTaskScroll();
      }
    });

    // 开始滚动
    startTaskScroll();
  }
};

// 初始化任务图表
const initTaskChart = () => {
  const chartDom = document.getElementById("taskChart");
  if (!chartDom) {
    console.log("Chart DOM element not found");
    return; // 这个return没问题，因为在函数内部
  }

  taskChart = echarts.init(chartDom);

  const option = {
    backgroundColor: "transparent",
    title: {
      text: "任务类型分布",
      left: "center",
      textStyle: {
        color: "#fff",
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    legend: {
      orient: "vertical",
      left: "left",
      textStyle: {
        color: "#ccc",
      },
    },
    series: [
      {
        name: "任务类型",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 4,
          borderColor: "#001529",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: "14",
            fontWeight: "bold",
            color: "#fff",
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 8, name: "例行巡逻", itemStyle: { color: "#1890ff" } },
          { value: 5, name: "设备维护", itemStyle: { color: "#52c41a" } },
          { value: 4, name: "安全检查", itemStyle: { color: "#faad14" } },
          { value: 2, name: "应急响应", itemStyle: { color: "#ff4d4f" } },
        ],
      },
    ],
  };

  taskChart.setOption(option);

  // 监听窗口大小变化，调整图表大小
  window.addEventListener("resize", () => {
    if (taskChart) {
      taskChart.resize();
    }
  });
};

// 计算当前页应该显示的车辆
const displayVehicles = computed(() => {
  const startIndex = currentPage.value * itemsPerPage;
  return vehiclesData.slice(startIndex, startIndex + itemsPerPage);
});

// 添加地图控制面板
const addMapControlPanel = (map: any, AMap: any) => {
  try {
    // 创建控制面板
    const controlPanel = document.createElement("div");
    controlPanel.className = "map-control-panel";
    controlPanel.innerHTML = `
      <div class="control-panel-buttons">
        <button class="control-btn traffic-btn">实时交通</button>
        <button class="control-btn path-btn">绘制路径</button>
        <button class="control-btn fence-btn">电子围栏</button>
        <button class="control-btn reset-btn">重置地图</button>
      </div>
    `;

    // 直接添加到地图容器
    const mapContainer = map.getContainer();
    if (mapContainer) {
      controlPanel.style.position = "absolute";
      controlPanel.style.left = "10px";
      controlPanel.style.top = "10px";
      controlPanel.style.zIndex = "100";
      mapContainer.appendChild(controlPanel);
    } else {
      console.error("无法获取地图容器");
      return;
    }

    // 获取按钮元素
    const trafficBtn = controlPanel.querySelector(".traffic-btn");
    const pathBtn = controlPanel.querySelector(".path-btn");
    const fenceBtn = controlPanel.querySelector(".fence-btn");
    const resetBtn = controlPanel.querySelector(".reset-btn");

    // 状态标记
    let trafficVisible = false;
    let mouseTool: any = null;
    let geofence: any = null;

    // 实时交通按钮事件
    if (trafficBtn) {
      trafficBtn.addEventListener("click", () => {
        if (trafficVisible) {
          map.remove(traffic);
          trafficBtn.classList.remove("active");
          trafficVisible = false;
        } else {
          map.add(traffic);
          trafficBtn.classList.add("active");
          trafficVisible = true;
        }
      });
    }

    // 绘制路径按钮事件
    if (pathBtn) {
      pathBtn.addEventListener("click", () => {
        try {
          // 重置其他绘制工具
          if (mouseTool) {
            mouseTool.close();
          }

          pathBtn.classList.add("active");
          if (fenceBtn) fenceBtn.classList.remove("active");

          // 创建绘制工具
          mouseTool = new AMap.MouseTool(map);
          mouseTool.polyline({
            strokeColor: "#28F",
            strokeOpacity: 1,
            strokeWeight: 6,
            strokeStyle: "solid",
            showDir: true,
          });

          // 监听绘制完成事件
          mouseTool.on("draw", function (e: any) {
            try {
              const path = e.obj.getPath();
              // 清除现有路径
              if (carPath) {
                map.remove(carPath);
              }
              carPath = e.obj;

              // 如果有标记，可以模拟车辆沿路径移动
              if (markers.length > 0) {
                const firstMarker = markers[0];
                firstMarker.moveAlong(path, {
                  duration: 10000,
                  autoRotation: true,
                });
              }
            } catch (drawError) {
              console.error("路径绘制处理错误:", drawError);
            } finally {
              // 关闭绘制工具
              mouseTool.close();
              pathBtn.classList.remove("active");
            }
          });
        } catch (error) {
          console.error("启动路径绘制工具失败:", error);
          pathBtn.classList.remove("active");
        }
      });
    }

    // 电子围栏按钮事件
    if (fenceBtn) {
      fenceBtn.addEventListener("click", () => {
        try {
          // 重置其他绘制工具
          if (mouseTool) {
            mouseTool.close();
          }

          fenceBtn.classList.add("active");
          if (pathBtn) pathBtn.classList.remove("active");

          // 创建绘制工具
          mouseTool = new AMap.MouseTool(map);
          mouseTool.polygon({
            strokeColor: "#FF4500",
            strokeOpacity: 1,
            strokeWeight: 2,
            fillColor: "#FF4500",
            fillOpacity: 0.3,
          });

          // 监听绘制完成事件
          mouseTool.on("draw", function (e: any) {
            try {
              // 清除现有围栏
              if (geofence) {
                map.remove(geofence);
              }
              geofence = e.obj;

              // 设置围栏事件监听
              setGeofenceListener(map, AMap, geofence);
            } catch (drawError) {
              console.error("围栏绘制处理错误:", drawError);
            } finally {
              // 关闭绘制工具
              mouseTool.close();
              fenceBtn.classList.remove("active");
            }
          });
        } catch (error) {
          console.error("启动围栏绘制工具失败:", error);
          fenceBtn.classList.remove("active");
        }
      });
    }

    // 重置地图按钮事件
    if (resetBtn) {
      resetBtn.addEventListener("click", () => {
        try {
          // 清除绘制工具
          if (mouseTool) {
            mouseTool.close();
          }

          // 清除路径
          if (carPath) {
            map.remove(carPath);
            carPath = null;
          }

          // 清除围栏
          if (geofence) {
            map.remove(geofence);
            geofence = null;
          }

          // 移除交通图层
          if (trafficVisible && traffic) {
            map.remove(traffic);
            if (trafficBtn) trafficBtn.classList.remove("active");
            trafficVisible = false;
          }

          // 重置按钮状态
          if (pathBtn) pathBtn.classList.remove("active");
          if (fenceBtn) fenceBtn.classList.remove("active");

          // 重新添加车辆标记
          try {
            clearMapMarkers();
            addVehicleMarkers(map, AMap);
          } catch (markersError) {
            console.error("重新添加车辆标记失败:", markersError);
          }

          // 重置地图视角
          map.setZoom(13);
          map.setCenter([104.065735, 30.659462]);
        } catch (error) {
          console.error("重置地图失败:", error);
        }
      });
    }
  } catch (error) {
    console.error("添加地图控制面板失败:", error);
  }

  // 添加标准地图控件
  try {
    map.addControl(new AMap.Scale());
    map.addControl(
      new AMap.ToolBar({
        position: "RB",
      })
    );
  } catch (controlError) {
    console.error("添加标准地图控件失败:", controlError);
  }
};

// 设置电子围栏监听器
const setGeofenceListener = (map: any, AMap: any, geofence: any) => {
  if (!geofence || !map || markers.length === 0) return;

  // 创建定时器检查车辆是否在围栏内
  const checkInterval = setInterval(() => {
    markers.forEach((marker) => {
      const position = marker.getPosition();
      const isInside = AMap.GeometryUtil.isPointInRing(
        [position.getLng(), position.getLat()],
        geofence.getPath()
      );

      // 根据是否在围栏内修改标记样式
      if (isInside) {
        // 如果车辆在围栏内，可以添加特殊样式或弹出提示
        if (!marker.isInGeofence) {
          marker.isInGeofence = true;
          // 创建报警提示
          const alertInfo = document.createElement("div");
          alertInfo.className = "geofence-alert";
          alertInfo.innerHTML = `<div>车辆 ${marker.getTitle()} 进入电子围栏区域</div>`;
          const alertMarker = new AMap.Marker({
            position: position,
            content: alertInfo,
            offset: new AMap.Pixel(0, -50),
            map: map,
          });

          // 3秒后移除提示
          setTimeout(() => {
            map.remove(alertMarker);
          }, 3000);
        }
      } else {
        // 如果车辆离开围栏，重置标记
        if (marker.isInGeofence) {
          marker.isInGeofence = false;
        }
      }
    });
  }, 1000);

  // 保存定时器以便清除
  geofence.checkInterval = checkInterval;

  // 监听围栏移除事件
  geofence.on("remove", () => {
    if (geofence.checkInterval) {
      clearInterval(geofence.checkInterval);
    }
  });
};

// 根据页码获取对应页的车辆数据
const getPageVehicles = (page: number) => {
  const startIndex = page * itemsPerPage;
  return vehiclesData.slice(startIndex, startIndex + itemsPerPage);
};

// 判断车辆是否可见
const isVehicleVisible = (index: number) => {
  const startIndex = currentPage.value * itemsPerPage;
  return index >= startIndex && index < startIndex + itemsPerPage;
};

// 添加显示信息窗口的辅助函数
const showVehicleInfoWindow = (extData: any, marker: any) => {
  if (!map) return;

  try {
    // 创建一个DOM元素作为信息窗口的内容
    const contentDiv = document.createElement("div");
    contentDiv.style.cssText =
      "background: linear-gradient(135deg, #001529, #003366); padding: 15px; border-radius: 8px; box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6); max-width: 300px; color: #fff; border: 1px solid rgba(24, 144, 255, 0.5); animation: fadeIn 0.3s ease-out;";

    // 添加必要的动画样式
    const styleEl = document.createElement("style");
    styleEl.textContent = `
      @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
      }
      @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
      }
      .info-highlight {
        position: relative;
      }
      .info-highlight:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(24, 144, 255, 0.2);
        border-radius: 4px;
        animation: pulse 2s infinite;
        z-index: -1;
      }
    `;
    document.head.appendChild(styleEl);

    // 创建标题元素
    const titleDiv = document.createElement("div");
    titleDiv.style.cssText =
      "font-size: 18px; font-weight: bold; color: #1890ff; margin-bottom: 12px; display: flex; align-items: center; border-bottom: 1px solid rgba(24, 144, 255, 0.5); padding-bottom: 10px; position: relative;";

    // 添加标题装饰元素
    const titleDecoration = document.createElement("div");
    titleDecoration.style.cssText =
      "position: absolute; bottom: 0; left: 0; height: 3px; width: 40px; background: linear-gradient(90deg, #1890ff, transparent); border-radius: 3px;";
    titleDiv.appendChild(titleDecoration);

    // 添加图标
    const iconSpan = document.createElement("span");
    iconSpan.style.cssText =
      "margin-right: 8px; background-color: #1890ff; width: 20px; height: 20px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 12px;";
    iconSpan.innerHTML =
      '<svg viewBox="0 0 1024 1024" width="14" height="14"><path d="M512 85.333333c-164.949333 0-298.666667 133.738667-298.666667 298.666667 0 164.949333 298.666667 554.666667 298.666667 554.666667s298.666667-389.717333 298.666667-554.666667c0-164.928-133.717333-298.666667-298.666667-298.666667z m0 448a149.333333 149.333333 0 1 1 0-298.666666 149.333333 149.333333 0 0 1 0 298.666666z" fill="#ffffff"></path></svg>';

    titleDiv.appendChild(iconSpan);

    const titleText = document.createElement("span");
    titleText.textContent = extData.licensePlate;
    titleDiv.appendChild(titleText);

    // 添加状态指示器
    const statusIndicator = document.createElement("span");
    const isCharging = extData.chargingStatus;
    const statusColor = isCharging ? "#faad14" : "#52c41a";
    statusIndicator.style.cssText = `margin-left: auto; display: flex; align-items: center; font-size: 12px; color: ${statusColor};`;

    // 添加状态图标
    const statusDot = document.createElement("span");
    statusDot.style.cssText = `display: inline-block; width: 8px; height: 8px; border-radius: 50%; background-color: ${statusColor}; margin-right: 5px;`;
    statusIndicator.appendChild(statusDot);

    // 添加状态文本
    const statusText = document.createElement("span");
    statusText.textContent = isCharging ? "充电中" : "行驶中";
    statusIndicator.appendChild(statusText);

    titleDiv.appendChild(statusIndicator);
    contentDiv.appendChild(titleDiv);

    // 创建内容区域
    const infoList = document.createElement("div");
    infoList.style.cssText = "font-size: 14px; color: rgba(255, 255, 255, 0.85);";

    // 添加信息项
    const infoItems = [
      {
        label: "车辆名称",
        value: extData.deviceName,
        icon:
          '<svg viewBox="0 0 1024 1024" width="14" height="14"><path d="M832 64H192c-70.4 0-128 57.6-128 128v640c0 70.4 57.6 128 128 128h640c70.4 0 128-57.6 128-128V192c0-70.4-57.6-128-128-128z m50.24 638.72l-50.24 50.56-102.4-102.4h-20.48v20.48l102.4 102.4-50.56 50.24-102.4-102.4h-40.96v40.96l81.92 81.92-50.56 50.24-81.92-81.92h-122.24v-80.64h-81.28v-81.28h-80.64v-122.88l-81.92-81.92 50.56-50.24 81.92 81.92h40.96v-40.96l-102.4-102.4 50.24-50.56 102.4 102.4h20.48v-20.48l-102.4-102.4 50.56-50.24 102.4 102.4h40.96v-40.96l-81.92-81.92 50.56-50.24 81.92 81.92h122.24v80.64h81.28v81.28h80.64v122.88l81.92 81.92-50.56 50.24-81.92-81.92h-40.96v40.96l102.4 102.4z" fill="#1890ff"></path></svg>',
      },
      {
        label: "电量",
        value: `${extData.batteryLevel}%`,
        icon:
          '<svg viewBox="0 0 1024 1024" width="14" height="14"><path d="M330.24 885.76h363.52V384H330.24v501.76z m417.28-623.04h90.88v623.04c0 27.136-21.504 49.92-48.64 49.92H234.24c-27.136 0-48.64-22.784-48.64-49.92V262.72h90.88v-75.52c0-24.576 19.456-44.032 44.032-44.032h383.744c24.576 0 44.032 19.456 44.032 44.032v75.52zM682.24 213.76H341.76v48.64h340.48v-48.64z" fill="#1890ff"></path></svg>',
      },
      {
        label: "速度",
        value: `${extData.currentSpeed} km/h`,
        icon:
          '<svg viewBox="0 0 1024 1024" width="14" height="14"><path d="M512 64C264.8 64 64 264.8 64 512s200.8 448 448 448 448-200.8 448-448S759.2 64 512 64z m0 736c-158.8 0-288-129.2-288-288s129.2-288 288-288 288 129.2 288 288-129.2 288-288 288z" fill="#1890ff"></path><path d="M555.2 724.8c-6.4 0-12-2.4-16.8-7.2-4.8-4.8-8-12-7.2-18.4 0.8-12 10.4-22.4 22.4-24 0.8 0 1.6 0 2.4 0 12 0 22.4 8.8 24 20.8 0.8 6.4-0.8 13.6-5.6 19.2-4.8 6.4-12 9.6-19.2 9.6zM512 352c-13.6 0-24-10.4-24-24V136c0-13.6 10.4-24 24-24s24 10.4 24 24v192c0 13.6-10.4 24-24 24zM352 512c0-13.6 10.4-24 24-24h192c13.6 0 24 10.4 24 24s-10.4 24-24 24H376c-13.6 0-24-10.4-24-24z" fill="#1890ff"></path></svg>',
      },
      {
        label: "位置",
        value: extData.location || "实时定位中",
        icon:
          '<svg viewBox="0 0 1024 1024" width="14" height="14"><path d="M844.8 814.4l-25.6-172.8c-1.6-8-8-14.4-16-16l-92.8-14.4c-4.8-14.4-9.6-27.2-16-40l46.4-84.8c4.8-8 3.2-17.6-3.2-24l-126.4-126.4c-6.4-6.4-16-8-24-3.2l-84.8 46.4c-12.8-6.4-27.2-11.2-40-16l-14.4-96c-1.6-8-8-14.4-16-16L259.2 220.8c-8-1.6-17.6 3.2-22.4 11.2l-56 121.6c-14.4 0-28.8 1.6-43.2 3.2L52.8 310.4c-8-4.8-17.6-3.2-24 3.2-6.4 6.4-8 16-3.2 24l46.4 84.8c-6.4 12.8-11.2 25.6-16 40l-96 14.4c-8 1.6-14.4 8-16 16l-25.6 172.8c-1.6 8 3.2 17.6 11.2 22.4l121.6 56c1.6 14.4 3.2 28.8 6.4 43.2l-54.4 83.2c-4.8 8-3.2 17.6 3.2 24 6.4 6.4 16 8 24 3.2l84.8-46.4c12.8 6.4 25.6 12.8 40 17.6l14.4 96c1.6 8 8 14.4 16 16l172.8 25.6h3.2c8 0 14.4-4.8 19.2-11.2l56-121.6c14.4 0 28.8-1.6 43.2-3.2l83.2 54.4c8 4.8 17.6 3.2 24-3.2 6.4-6.4 8-16 3.2-24l-46.4-84.8c6.4-12.8 12.8-25.6 17.6-40l96-14.4c8-1.6 14.4-8 16-16l25.6-172.8c1.6-8-3.2-16-11.2-20.8l-121.6-56c-1.6-14.4-3.2-28.8-6.4-43.2l54.4-83.2c4.8-8 3.2-17.6-3.2-24-6.4-6.4-16-8-24-3.2l-84.8 46.4c-12.8-6.4-25.6-12.8-40-17.6l-14.4-96c-1.6-8-8-14.4-16-16l-8-1.6 6.4 40 12.8 86.4c1.6 11.2 9.6 20.8 20.8 24 19.2 4.8 38.4 12.8 57.6 22.4 9.6 4.8 20.8 4.8 28.8-1.6l76.8-41.6-43.2 67.2c-6.4 9.6-6.4 20.8-1.6 30.4 8 16 14.4 33.6 19.2 52.8 3.2 11.2 12.8 19.2 24 20.8l86.4 12.8-17.6 120-86.4 12.8c-11.2 1.6-20.8 9.6-24 20.8-4.8 19.2-12.8 38.4-22.4 57.6-4.8 9.6-4.8 20.8 1.6 28.8l41.6 76.8-67.2-43.2c-9.6-6.4-20.8-6.4-30.4-1.6-16 8-33.6 14.4-52.8 19.2-11.2 3.2-19.2 12.8-20.8 24l-12.8 86.4-118.4-17.6-12.8-86.4c-1.6-11.2-9.6-20.8-20.8-24-19.2-4.8-36.8-12.8-52.8-22.4-9.6-4.8-20.8-4.8-28.8 1.6l-76.8 41.6 43.2-65.6c6.4-9.6 6.4-20.8 1.6-30.4-8-16-14.4-33.6-19.2-52.8-3.2-11.2-12.8-19.2-24-20.8l-88-12.8 17.6-118.4 86.4-12.8c11.2-1.6 20.8-9.6 24-20.8 3.2-19.2 12.8-36.8 22.4-52.8 4.8-9.6 4.8-20.8-1.6-28.8l-41.6-76.8 65.6 43.2c9.6 6.4 20.8 6.4 30.4 1.6 16-8 33.6-14.4 52.8-19.2 11.2-3.2 19.2-12.8 20.8-24l12.8-88 120 17.6z" fill="#1890ff"></path><path d="M512 400c-62.4 0-112 49.6-112 112s49.6 112 112 112 112-49.6 112-112-49.6-112-112-112z m0 160c-27.2 0-48-20.8-48-48s20.8-48 48-48 48 20.8 48 48-20.8 48-48 48z" fill="#1890ff"></path></svg>',
      },
      {
        label: "更新时间",
        value: new Date().toLocaleTimeString(),
        icon:
          '<svg viewBox="0 0 1024 1024" width="14" height="14"><path d="M512 0C229.2 0 0 229.2 0 512s229.2 512 512 512 512-229.2 512-512S794.8 0 512 0z m0 944c-238.8 0-432-193.2-432-432S273.2 80 512 80s432 193.2 432 432-193.2 432-432 432z" fill="#1890ff"></path><path d="M696 480h-144V256c0-22.4-17.6-40-40-40s-40 17.6-40 40v264c0 22.4 17.6 40 40 40h184c22.4 0 40-17.6 40-40s-17.6-40-40-40z" fill="#1890ff"></path></svg>',
      },
    ];

    infoItems.forEach((item) => {
      const itemDiv = document.createElement("div");
      itemDiv.style.cssText = "margin: 8px 0; display: flex; align-items: center;";

      // 添加图标
      const iconDiv = document.createElement("div");
      iconDiv.style.cssText = "flex: 0 0 25px; color: #1890ff;";
      iconDiv.innerHTML = item.icon;
      itemDiv.appendChild(iconDiv);

      // 添加标签
      const labelSpan = document.createElement("span");
      labelSpan.style.cssText = "color: rgba(255, 255, 255, 0.65); flex: 0 0 70px;";
      labelSpan.textContent = `${item.label}:`;
      itemDiv.appendChild(labelSpan);

      // 添加值
      const valueSpan = document.createElement("span");
      valueSpan.style.cssText = "flex: 1;";

      // 如果是电量，添加进度条
      if (item.label === "电量") {
        const batteryLevel = parseFloat(extData.batteryLevel);
        const batteryColor =
          batteryLevel <= 20 ? "#ff4d4f" : batteryLevel <= 50 ? "#faad14" : "#52c41a";

        const valueText = document.createElement("span");
        valueText.textContent = item.value;
        valueText.style.marginRight = "5px";

        const progressContainer = document.createElement("div");
        progressContainer.style.cssText =
          "display: inline-block; width: 80px; height: 8px; background: rgba(0, 0, 0, 0.25); border-radius: 4px; overflow: hidden; vertical-align: middle; box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);";

        const progressBar = document.createElement("div");
        progressBar.style.cssText = `width: ${batteryLevel}%; height: 100%; background: linear-gradient(90deg, ${batteryColor}, ${batteryColor}bb); transition: width 1s ease-in-out;`;

        // 为低电量添加闪烁效果
        if (batteryLevel <= 20) {
          progressBar.style.animation = "pulse 2s infinite";
        }

        progressContainer.appendChild(progressBar);
        valueSpan.appendChild(valueText);
        valueSpan.appendChild(progressContainer);
      } else {
        valueSpan.textContent = item.value;
      }

      itemDiv.appendChild(valueSpan);
      infoList.appendChild(itemDiv);
    });

    contentDiv.appendChild(infoList);

    // 添加操作按钮区域
    const buttonArea = document.createElement("div");
    buttonArea.style.cssText =
      "display: flex; justify-content: flex-end; margin-top: 15px; border-top: 1px solid rgba(24, 144, 255, 0.3); padding-top: 10px;";

    // 添加详情按钮
    const detailButton = document.createElement("button");
    detailButton.textContent = "详情";
    detailButton.style.cssText =
      "background: #1890ff; border: none; color: white; padding: 5px 14px; border-radius: 4px; margin-left: 10px; cursor: pointer; font-size: 12px; font-weight: bold; transition: all 0.3s; box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4);";

    // 添加鼠标悬停效果
    detailButton.onmouseover = () => {
      detailButton.style.backgroundColor = "#40a9ff";
      detailButton.style.boxShadow = "0 4px 12px rgba(24, 144, 255, 0.6)";
    };
    detailButton.onmouseout = () => {
      detailButton.style.backgroundColor = "#1890ff";
      detailButton.style.boxShadow = "0 2px 6px rgba(24, 144, 255, 0.4)";
    };

    detailButton.onclick = () => {
      if (window.showVehicleDetails) {
        window.showVehicleDetails(extData.deviceName);
      }
    };

    // 添加跟踪按钮
    const trackButton = document.createElement("button");
    trackButton.textContent = "跟踪";
    trackButton.style.cssText =
      "background: rgba(0, 21, 41, 0.6); border: 1px solid #1890ff; color: #1890ff; padding: 5px 14px; border-radius: 4px; cursor: pointer; font-size: 12px; font-weight: bold; transition: all 0.3s;";

    // 添加鼠标悬停效果
    trackButton.onmouseover = () => {
      trackButton.style.backgroundColor = "rgba(24, 144, 255, 0.15)";
      trackButton.style.borderColor = "#40a9ff";
      trackButton.style.color = "#40a9ff";
    };
    trackButton.onmouseout = () => {
      trackButton.style.backgroundColor = "rgba(0, 21, 41, 0.6)";
      trackButton.style.borderColor = "#1890ff";
      trackButton.style.color = "#1890ff";
    };

    trackButton.onclick = () => {
      if (window.trackVehicle) {
        window.trackVehicle(extData.deviceName);
      }
    };

    buttonArea.appendChild(trackButton);
    buttonArea.appendChild(detailButton);
    contentDiv.appendChild(buttonArea);

    // 创建信息窗口
    const infoWindow = new window.AMap.InfoWindow({
      content: contentDiv,
      offset: new window.AMap.Pixel(0, -32),
      closeWhenClickMap: true, // 点击地图时关闭
    });

    // 打开信息窗口
    infoWindow.open(map, marker.getPosition());

    // 调试信息窗口DOM结构的辅助函数
    setTimeout(() => {
      // 获取所有信息窗口元素
      const infoWindows = document.querySelectorAll(".amap-info-content");
      if (infoWindows.length > 0) {
        // 打印DOM结构到控制台
        console.log("信息窗口DOM结构:", infoWindows[infoWindows.length - 1]);

        // 可以在此处查看或修改最新打开的信息窗口
        const latestInfoWindow = infoWindows[infoWindows.length - 1];

        // 输出窗口的样式
        console.log("信息窗口计算样式:", window.getComputedStyle(latestInfoWindow));
      }
    }, 100); // 短暂延迟确保DOM已经创建

    return infoWindow;
  } catch (error) {
    console.error("显示信息窗口失败:", error);
    return null;
  }
};

// 检查车辆通信状态
const checkVehicleCommunication = () => {
  const now = Date.now();
  const timeout = 10000; // 10秒超时

  vehiclesData.forEach((vehicle) => {
    const lastComm = lastVehicleCommunication.get(vehicle.id);

    if (lastComm && now - lastComm > timeout && vehicle.status !== "offline") {
      // 车辆超过10秒未通信，标记为离线
      vehicle.status = "offline";

      // 发送通知 - 3秒后自动消失
      ElNotification({
        title: "车辆通信中断",
        message: `车辆 ${vehicle.id} 已超过10秒未收到信息，请检查通信状态`,
        type: "warning",
        duration: 3000, // 3秒后自动消失
      });

      // 更新地图上的标记为灰色
      const marker = vehicleMarkers.get(vehicle.id);
      if (marker) {
        // 更新标记的样式以表示离线状态
        const extData = marker.getExtData();
        marker.setExtData({
          ...extData,
          status: "offline",
        });

        // 更改图标为灰色版本
        marker.setIcon(
          new window.AMap.Icon({
            // 使用灰色版本的图标
            image: "https://a.amap.com/jsapi_demos/static/demo-center-v2/car.png",
            size: new window.AMap.Size(26, 52),
            imageSize: new window.AMap.Size(26, 52),
            imageOffset: new window.AMap.Pixel(0, 0),
            // 添加灰色滤镜
            imageFilter: "grayscale(100%)",
          })
        );

        // 如果有文本标记，也更新为灰色
        if (marker.textMarker) {
          marker.textMarker.setStyle({
            "background-color": "#999",
            color: "#ddd",
          });
        }
      }

      // 为对应的车辆卡片添加闪烁效果
      addOfflineFlashingEffect(vehicle.id);
    }
  });
};

// 为离线车辆卡片添加闪烁效果
const addOfflineFlashingEffect = (vehicleId: string) => {
  // 等待下一个DOM更新周期，确保车辆卡片已经更新为离线状态
  nextTick(() => {
    // 查找所有车辆卡片
    const vehicleCards = document.querySelectorAll(".vehicle-card");
    vehicleCards.forEach((card: Element) => {
      // 查找卡片标题元素
      const titleEl = card.querySelector(".vehicle-title");
      if (titleEl && titleEl.textContent && titleEl.textContent.includes(vehicleId)) {
        // 添加闪烁类
        card.classList.add("vehicle-offline-flashing");

        // 10秒后移除闪烁效果
        setTimeout(() => {
          card.classList.remove("vehicle-offline-flashing");
        }, 10000);
      }
    });
  });
};
</script>

<style lang="less">
/* 覆盖高德地图信息窗口样式 */
.amap-info-content {
  background: linear-gradient(135deg, #001529, #003366) !important;
  color: #fff !important;
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6) !important;
  border: 1px solid rgba(24, 144, 255, 0.5) !important;
}

.amap-info-close {
  color: rgba(255, 255, 255, 0.85) !important;
  background: rgba(0, 21, 41, 0.7) !important;
  border-radius: 50% !important;
  top: 8px !important;
  right: 8px !important;
}

.amap-info-sharp {
  background-color: #001f3f !important;
}

/* 离线车辆闪烁效果 */
.vehicle-offline-flashing {
  animation: offlineFlashing 1s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(255, 0, 0, 0.5);
}

@keyframes offlineFlashing {
  0% {
    border-color: rgba(255, 0, 0, 0.3);
  }
  50% {
    border-color: rgba(255, 0, 0, 0.9);
    box-shadow: 0 0 15px rgba(255, 0, 0, 0.8);
  }
  100% {
    border-color: rgba(255, 0, 0, 0.3);
  }
}
</style>

<style lang="less" scoped>
#container {
  width: 100%;
  height: 50vh;
}
.patrol-dashboard {
  width: 100%;
  height: 100vh;
  background-color: #001529;
  color: white;
  font-family: "Microsoft YaHei", sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: radial-gradient(ellipse at center, #0a2e50 0%, #001529 100%);
  position: relative; /* 添加相对定位 */
}

// 顶部导航栏
.top-bar {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  background: linear-gradient(90deg, rgba(0, 21, 41, 0.9), rgba(0, 64, 87, 0.8));
  border-bottom: 1px solid rgba(24, 144, 255, 0.2);
  box-shadow: 0 1px 10px rgba(0, 0, 0, 0.3);

  .logo {
    display: flex;
    align-items: center;

    .logo-image {
      width: 36px;
      height: 36px;
      margin-right: 10px;
      background: linear-gradient(135deg, #1890ff, #0a2e50);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 6px rgba(24, 144, 255, 0.3);

      .logo-icon {
        font-size: 20px;
        color: white;
      }
    }

    .system-name {
      font-size: 18px;
      font-weight: 600;
      background: linear-gradient(90deg, #1890ff, #52c41a);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
    }
  }

  .top-actions {
    display: flex;
    align-items: center;

    .weather {
      font-size: 14px;
      display: flex;
      align-items: center;
      margin-right: 20px;
      background: rgba(0, 0, 0, 0.2);
      padding: 4px 12px;
      border-radius: 20px;

      i {
        margin-right: 8px;
        color: #faad14;
        font-size: 18px;
      }
    }

    i {
      margin-left: 20px;
      cursor: pointer;
      font-size: 18px;
      opacity: 0.8;
      transition: all 0.3s;

      &:hover {
        color: #1890ff;
        opacity: 1;
        transform: scale(1.1);
      }
    }
  }
}

// 状态卡片区域
.status-cards {
  display: flex;
  padding: 15px;
  gap: 15px;
  height: 130px; /* 增加高度，确保足够空间 */
  flex-shrink: 0; /* 防止高度被压缩 */
  width: 100%; /* 确保宽度填满 */
  box-sizing: border-box; /* 确保padding不增加总宽度 */

  .status-card {
    flex: 1;
    background: linear-gradient(135deg, rgba(0, 21, 41, 0.8), rgba(0, 30, 60, 0.7));
    border: 1px solid rgba(24, 144, 255, 0.2);
    border-radius: 8px;
    padding: 15px;
    padding-right: 20px; /* 右侧增加填充 */
    padding-bottom: 20px; /* 底部增加填充，避免进度条被截断 */
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    overflow: visible; /* 确保内容不被截断 */
    position: relative; /* 允许绝对定位内部元素 */
    min-width: 0; /* 允许flex子项收缩 */

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
      border-color: rgba(24, 144, 255, 0.4);
    }

    .card-title {
      font-size: 14px;
      margin-bottom: 5px;
      color: rgba(255, 255, 255, 0.65);
    }

    .card-value {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 10px;

      .unit {
        font-size: 14px;
        font-weight: normal;
        opacity: 0.8;
        margin-left: 2px;
      }
    }

    .completion-rate {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.65);

      .progress-bar {
        height: 5px;
        flex: 1;
        background-color: rgba(255, 255, 255, 0.1);
        margin: 0 8px;
        border-radius: 3px;
        overflow: hidden;

        .progress {
          height: 100%;
          background-color: #1890ff;
          transition: width 0.5s ease;
        }
      }
    }

    .stat-trend {
      display: flex;
      align-items: center;
      font-size: 12px;

      &.increasing {
        color: #52c41a;
      }

      &.decreasing {
        color: #ff4d4f;
      }

      i {
        margin-right: 4px;
      }
    }

    &.warning-card {
      .warning-message {
        display: flex;
        align-items: center;
        font-size: 14px;
        flex-wrap: wrap;
        margin-bottom: 8px;

        i {
          color: #ff4d4f;
          font-size: 20px;
          margin-right: 5px;
          flex-shrink: 0; /* 防止图标被压缩 */
        }

        .red-text {
          color: #ff4d4f;
          word-wrap: break-word; /* 允许长文本换行 */
          flex: 1; /* 占据剩余空间 */
          min-width: 0; /* 支持弹性布局中的溢出处理 */
        }
      }

      .warning-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;

        .action-link {
          color: #1890ff;
          cursor: pointer;
          font-size: 12px;

          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}

// 搜索栏
.search-bar {
  margin: 0 15px 15px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 0 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 40px; /* 固定高度 */
  flex-shrink: 0; /* 防止高度被压缩 */

  i {
    color: rgba(255, 255, 255, 0.5);
  }

  input {
    flex: 1;
    background: transparent;
    border: none;
    color: white;
    padding: 10px;
    outline: none;

    &::placeholder {
      color: rgba(255, 255, 255, 0.3);
    }
  }
}

// 主内容区域
.content-layout {
  display: flex;
  flex: 1;
  padding: 0 15px 15px 15px;
  margin-right: 295px; /* 为右侧监控区域留出空间 */
  min-height: 400px; /* 确保主内容区有足够的高度 */
  flex-shrink: 0; /* 防止高度被压缩 */
}

// 地图区域
.map-container {
  flex: 1; /* 占据主内容区域的所有空间 */
  position: relative;
  background: linear-gradient(135deg, rgba(0, 21, 41, 0.7), rgba(0, 30, 60, 0.6));
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(24, 144, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
}

// 监控区域
.monitor-area {
  position: absolute;
  top: 185px; /* 调整顶部位置，与地图区域顶部对齐 */
  right: 15px;
  width: 280px;
  bottom: 70px; /* 底部对齐任务列表底部 */
  display: flex;
  flex-direction: column;
  gap: 15px;

  .monitor-card {
    flex: 1;
    background: linear-gradient(135deg, rgba(0, 21, 41, 0.7), rgba(0, 30, 60, 0.6));
    border-radius: 0px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(24, 144, 255, 0.2);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    position: relative;

    // 添加边缘光效
    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(24, 144, 255, 0.3),
        transparent
      );
      z-index: 1;
    }

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0.5),
        rgba(0, 30, 60, 0.8),
        rgba(0, 0, 0, 0.5)
      );
      z-index: 1;
    }

    :deep(.el-card__header) {
      padding: 0;
      margin: 0;
      background: linear-gradient(90deg, rgba(0, 21, 41, 0.95), rgba(0, 0, 0, 0.7));
      border-bottom: 1px solid rgba(24, 144, 255, 0.3);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .monitor-header {
      display: flex;
      justify-content: space-between;
      padding: 4px 10px;
      background: rgba(0, 0, 0, 0.3);
      font-size: 14px;
      border-bottom: 1px solid rgba(24, 144, 255, 0.2);
      align-items: center;

      span {
        font-size: 13px;
        color: rgba(255, 255, 255, 0.9);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        font-weight: 500;
        letter-spacing: 0.5px;
      }

      .camera-select {
        display: flex;
        align-items: center;
        font-size: 13px;
        background: rgba(0, 0, 0, 0.2);
        padding: 2px 6px;
        border-radius: 2px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid rgba(24, 144, 255, 0.2);

        &:hover {
          background: rgba(24, 144, 255, 0.2);
          color: #fff;
          border-color: rgba(24, 144, 255, 0.5);
          box-shadow: 0 0 5px rgba(24, 144, 255, 0.3);
        }

        .el-icon {
          margin-left: 4px;
          font-size: 12px;
        }
      }
    }

    .monitor-screen {
      height: 233.67px;
      background-color: #000;
      position: relative;
      overflow: hidden;

      .camera-feed {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
          0deg,
          rgba(0, 0, 0, 0.2),
          rgba(0, 0, 0, 0.2) 1px,
          rgba(0, 0, 0, 0) 1px,
          rgba(0, 0, 0, 0) 2px
        );
        background-color: rgba(0, 30, 60, 0.5);

        /* 删除或注释掉原来的伪元素样式，避免遮挡视频 */
        /*
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1), transparent 40%),
          radial-gradient(circle at 30% 70%, rgba(0, 100, 255, 0.1), transparent 40%);
          animation: cameraNoise 8s infinite;
        }

        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(rgba(0, 30, 60, 0.5),
          rgba(0, 30, 60, 0.2));
          animation: cameraFlicker 10s infinite;
        }
        */

        // 添加视频播放器样式
        .flv-player {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        // 添加加载中覆盖层样式
        .video-loading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 21, 41, 0.7);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 2;

          .loading-spinner {
            width: 30px;
            height: 30px;
            border: 3px solid rgba(24, 144, 255, 0.3);
            border-radius: 50%;
            border-top-color: #1890ff;
            animation: spin 1s ease-in-out infinite;
            margin-bottom: 8px;
          }

          .loading-text {
            color: #fff;
            font-size: 12px;
          }
        }

        // 添加错误覆盖层样式
        .video-error-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 21, 41, 0.7);
          display: none;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 2;

          .error-icon {
            color: #ff4d4f;
            font-size: 20px;
            margin-bottom: 8px;
          }

          .error-text {
            color: #fff;
            font-size: 12px;
            margin-bottom: 10px;
          }
        }

        // 添加播放按钮覆盖层样式
        .video-play-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: none;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          z-index: 3;
          cursor: pointer;

          .play-button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: rgba(24, 144, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 8px;
            transition: transform 0.2s, background-color 0.2s;

            svg {
              margin-left: 4px; /* 稍微向右偏移，使播放图标视觉上居中 */
            }

            &:hover {
              transform: scale(1.1);
              background-color: rgba(24, 144, 255, 0.9);
            }
          }

          .play-text {
            color: #fff;
            font-size: 12px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
          }

          &:hover {
            background-color: rgba(0, 0, 0, 0.6);
          }
        }

        .camera-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          padding: 10px;
          z-index: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          pointer-events: none; /* 确保覆盖层不会阻挡视频的交互 */

          .camera-info {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
            background-color: rgba(0, 0, 0, 0.3);
            padding: 3px 6px;
            border-radius: 4px;
            margin-right: 5px;
          }

          .camera-status {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            background-color: rgba(0, 0, 0, 0.3);
            padding: 3px 6px;
            border-radius: 4px;
            align-self: flex-end;

            .rec-dot {
              width: 8px;
              height: 8px;
              background-color: #ff4d4f;
              border-radius: 50%;
              animation: blink 1s infinite;
            }
          }
        }

        &.type-2,
        &.type-3 {
          /* 保留不同摄像头类型的样式特点，但避免使用伪元素 */
          .flv-player {
            filter: contrast(1.1) saturate(1.2);
          }
        }

        &.type-3 {
          .flv-player {
            filter: hue-rotate(30deg) contrast(1.2);
          }
        }
      }
    }
  }
}

// 添加旋转动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 车辆信息区域
.vehicle-info-area {
  padding: 0 15px 15px;
  margin-right: 295px; /* 为右侧监控区域留出空间 */
  display: flex;
  flex-direction: column;
  height: auto; /* 自适应高度 */
  flex-shrink: 0; /* 防止高度被压缩 */

  .vehicle-carousel {
    position: relative;
    flex: 1;
    overflow: hidden;
    margin-bottom: 1rem;
    height: 220px;
  }

  .carousel-container {
    position: relative;
    width: 100%;
    overflow: hidden;
  }

  .vehicle-cards {
    display: flex;
    width: 100%;
    transition: transform 0.5s ease;
    /* 修复宽度计算，允许所有页面显示 */
    width: calc(100% * 3); /* 使用固定值替代模板字符串 */
  }

  .vehicle-page {
    margin-top: 20px;
    display: flex;
    flex-wrap: nowrap;
    width: 100%;
  }

  .vehicle-card {
    flex: 0 0 calc(25% - 15px);
    min-width: calc(25% - 15px);
    max-width: calc(25% - 15px);
    height: 160px;
    background: rgba(0, 21, 41, 0.5);
    padding: 12px;
    transition: all 0.3s ease;
    margin: 0 7.5px;
    border-radius: 6px;
    border: 1px solid rgba(24, 144, 255, 0.3);
    display: flex;
    flex-direction: column;
  }

  .vehicle-card:hover {
    background: rgba(24, 144, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    border-color: rgba(24, 144, 255, 0.5);
  }

  .vehicle-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .vehicle-title {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .vehicle-status {
      padding: 0 8px;
      height: 20px;
      line-height: 20px;
      border-radius: 10px;
      font-size: 12px;
      margin-left: 8px;
    }
  }

  .vehicle-location {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.6);
    margin-top: 5px;
    margin-bottom: 15px;

    .el-icon {
      margin-right: 5px;
      color: #1890ff;
    }
  }

  .vehicle-details {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(2, 1fr); /* 创建两列网格 */
    grid-gap: 10px; /* 增加网格间距 */

    .detail-item {
      .detail-label {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.5);
        display: block;
        margin-bottom: 5px;
      }

      .detail-value {
        font-size: 14px;
        font-weight: 500;
        display: block;
        color: #fff;
        margin-bottom: 5px;
      }

      .battery-bar {
        height: 6px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 3px;
        overflow: hidden;
        margin-top: 3px;
      }
    }
  }

  .carousel-controls {
    position: absolute;
    top: 45%;
    transform: translateY(-50%);
    right: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 5px;

    .carousel-btn {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(24, 144, 255, 0.3);
      color: white;
      border: none;
      border-radius: 50%;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      transition: all 0.3s ease;
      z-index: 10;

      &:hover {
        transform: scale(1.1);
        background: rgba(24, 144, 255, 0.5);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;

        &:hover {
          transform: none;
          background: rgba(24, 144, 255, 0.3);
        }
      }

      &.prev-btn {
        margin-left: 5px;
      }

      &.next-btn {
        margin-right: 5px;
      }
    }
  }

  .carousel-indicators {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 6px;

    .indicator-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background-color: #1890ff;
        transform: scale(1.2);
      }

      &:hover {
        background-color: rgba(24, 144, 255, 0.7);
      }
    }
  }

  // 任务列表
  .task-list-card {
    background: linear-gradient(135deg, rgba(0, 21, 41, 0.7), rgba(0, 30, 60, 0.6));
    border-radius: 8px;
    overflow: auto; /* 允许内容溢出时滚动 */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(24, 144, 255, 0.2);
    // min-height: 230px; /* 最小高度 */
    // max-height: 300px; /* 最大高度 */
    flex-shrink: 0; /* 防止高度被压缩 */
    height: auto;
  }
}

// 进度条大尺寸版本
.progress-bar.large {
  height: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 5px;
}

.progress-bar.large .progress {
  height: 100%;
  background-color: #1890ff;
  background-image: linear-gradient(90deg, #1890ff, #52c41a);
  transition: width 0.5s ease;
}

// 响应式处理
@media screen and (max-width: 1200px) {
  .content-layout {
    flex-direction: column;

    .map-container,
    .monitor-area {
      flex: none;
    }

    .map-container {
      height: 300px;
    }

    .monitor-area {
      flex-direction: row;
      height: 150px;
    }
  }

  .vehicle-info-area .vehicle-carousel .vehicle-cards .vehicle-card {
    min-width: 50%;
    max-width: 50%;
  }
}

@media screen and (max-width: 768px) {
  .vehicle-info-area .vehicle-carousel .vehicle-cards .vehicle-card {
    min-width: 100%;
    max-width: 100%;
  }
}

// 动画关键帧
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes glow {
  0% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    opacity: 0.5;
  }
}

@keyframes moveVehicle1 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  25% {
    transform: translate(-50%, -50%) translateX(50px) translateY(20px);
  }
  50% {
    transform: translate(-50%, -50%) translateX(100px) translateY(-30px);
  }
  75% {
    transform: translate(-50%, -50%) translateX(30px) translateY(-80px);
  }
  100% {
    transform: translate(-50%, -50%) translateX(-50px) translateY(-40px);
  }
}

@keyframes moveVehicle2 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  30% {
    transform: translate(-50%, -50%) translateX(-60px) translateY(40px);
  }
  60% {
    transform: translate(-50%, -50%) translateX(-120px) translateY(10px);
  }
  100% {
    transform: translate(-50%, -50%) translateX(-30px) translateY(-60px);
  }
}

@keyframes moveVehicle3 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  50% {
    transform: translate(-50%, -50%) translateX(80px) translateY(-40px);
  }
  100% {
    transform: translate(-50%, -50%) translateX(20px) translateY(-100px);
  }
}

@keyframes moveVehicle4 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateX(120px) translateY(80px);
  }
}

@keyframes moveVehicle5 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateX(-90px) translateY(-60px);
  }
}

@keyframes moveVehicle6 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateX(-60px) translateY(90px);
  }
}

@keyframes moveVehicle7 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateX(70px) translateY(50px);
  }
}

@keyframes moveVehicle8 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateX(80px) translateY(-70px);
  }
}

@keyframes moveVehicle9 {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  100% {
    transform: translate(-50%, -50%) translateX(100px) translateY(30px);
  }
}

@keyframes moveAmbulance {
  0% {
    transform: translate(-50%, -50%) translateX(0) translateY(0);
  }
  25% {
    transform: translate(-50%, -50%) translateX(80px) translateY(40px);
  }
  50% {
    transform: translate(-50%, -50%) translateX(40px) translateY(100px);
  }
  75% {
    transform: translate(-50%, -50%) translateX(-60px) translateY(60px);
  }
  100% {
    transform: translate(-50%, -50%) translateX(-100px) translateY(-30px);
  }
}

@keyframes cameraNoise {
  0%,
  100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes cameraFlicker {
  0%,
  100% {
    opacity: 1;
  }
  96% {
    opacity: 1;
  }
  97% {
    opacity: 0.8;
  }
  98% {
    opacity: 1;
  }
  99% {
    opacity: 0.5;
  }
}

@keyframes blink {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

// 地图标记标签样式
.map-vehicle-label {
  color: white;
  background: rgba(24, 144, 255, 0.8);
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  white-space: nowrap;
  margin-top: -5px;
}

// 全局样式处理，防止滚动条出现
:global(.el-main.rr-view) {
  padding: 0;
  overflow: hidden;
  height: 100vh;
}

:global(.rr-view-container) {
  height: 100vh;
  overflow: hidden;
}

:global(.rr-view) {
  overflow: hidden;
}

:global(.rr-view-ctx) {
  height: 100%;
}

:global(.rr-view-ctx-card) {
  height: 100%;
  overflow: hidden;
}

:global(.el-card__body) {
  height: 100%;
  padding: 0;
}

/* 地图控制面板样式 */
.map-control-panel {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  padding: 10px;
  margin: 10px;
  width: 140px;
}

.control-panel-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
  text-align: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.control-panel-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-btn {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 6px 8px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.control-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.control-btn.active {
  background: #1890ff;
  color: #fff;
  border-color: #1890ff;
}

.traffic-btn {
  background: linear-gradient(135deg, #fff, #f0f8ff);
}

.path-btn {
  background: linear-gradient(135deg, #fff, #f0fff0);
}

.fence-btn {
  background: linear-gradient(135deg, #fff, #fff0f0);
}

.reset-btn {
  background: linear-gradient(135deg, #fff, #f5f5f5);
}

/* 电子围栏警告样式 */
.geofence-alert {
  background: rgba(255, 69, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  animation: fadeInOut 3s ease-in-out;
  white-space: nowrap;
}

@keyframes fadeInOut {
  0%,
  100% {
    opacity: 0;
  }
  20%,
  80% {
    opacity: 1;
  }
}

/* 地图工具提示样式 */
.amap-tool-tip {
  background-color: rgba(0, 21, 41, 0.8) !important;
  color: white !important;
  border: 1px solid rgba(24, 144, 255, 0.3) !important;
  padding: 5px 10px !important;
  font-size: 12px !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
}
</style>
