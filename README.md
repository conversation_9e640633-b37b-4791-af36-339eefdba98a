# 车辆监控系统前端

## 项目概述
车辆监控系统前端界面，使用Vue.js和高德地图API，提供车辆实时位置跟踪、状态监控和数据展示功能。

## 环境要求
- Node.js >= 14.x
- npm >= 6.x

## 安装依赖
```bash
npm install
```

## 开发环境运行
```bash
npm run dev
```

## 生产环境构建
```bash
npm run build
```

## 项目核心功能

### WebSocket连接
系统使用原生WebSocket与后端建立实时连接，接收车辆位置和状态更新。

```typescript
// 连接示例
const socket = new WebSocket('ws://localhost:10001/raw-ws');

// 配置连接
socket.onopen = () => {
  console.log("WebSocket连接成功");
};

socket.onmessage = (event) => {
  const data = JSON.parse(event.data);
  // 处理接收到的数据
};
```

### 高德地图集成
项目集成了高德地图API，用于显示车辆位置和轨迹。

```typescript
// 初始化地图
AMapLoader.load({
  key: "您的高德地图API密钥",
  version: "2.0",
  plugins: ["AMap.Scale", "AMap.ToolBar", "AMap.MoveAnimation"]
}).then((AMap) => {
  // 创建地图实例
  const map = new AMap.Map("container", {
    viewMode: "3D",
    zoom: 15,
    center: [经度, 纬度]
  });
  
  // 添加车辆标记
  // ...
});
```

### 车辆信息轮播
使用Element Plus的el-carousel组件实现车辆信息轮播展示。

```html
<el-carousel 
  :interval="8000" 
  arrow="always" 
  indicator-position="outside"
  height="180px"
>
  <el-carousel-item v-for="page in totalPages" :key="page">
    <!-- 车辆信息内容 -->
  </el-carousel-item>
</el-carousel>
```

## 常见问题解决方案

### WebSocket连接问题
- 确保后端WebSocket服务已启动
- 确认WebSocket URL格式正确（ws://或wss://）
- 检查网络连接和防火墙设置

### 高德地图显示问题
- 确保已正确加载高德地图API
- 检查API密钥是否有效
- 地图容器必须有明确的宽高设置

### 车辆轮播问题
- 使用el-carousel组件时确保正确设置高度
- 设置合适的间隔时间(:interval属性)
- 对于多页轮播，正确计算每页显示的数量

## 项目结构说明
- `src/views`: 页面组件
- `src/components`: 通用组件
- `src/services`: 服务类，包含WebSocket连接等
- `src/utils`: 工具函数
- `src/assets`: 静态资源
- `src/router`: 路由配置
- `src/store`: 状态管理

## 贡献指南
1. Fork项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request
