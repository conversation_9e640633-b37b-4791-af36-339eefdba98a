<template>
  <div class="status-cards">
    <!-- 第一行：主要指标 -->
    <div class="primary-stats">
      <div class="stat-card">
        <div class="stat-title">在线车辆</div>
        <div class="stat-value">{{ monitoringData.onlineVehicles }} / {{ monitoringData.totalVehicles }}</div>
        <div class="progress-bar">
          <div class="progress-inner" :style="{ width: `${monitoringData.completionRate}%` }"></div>
        </div>
        <div class="stat-description">{{ monitoringData.completionRate }}% 车辆在线</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-title">今日总里程</div>
        <div class="stat-value">{{ monitoringData.todayDistance }}<span class="unit">km</span></div>
        <div class="stat-icon">🚗</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-title">任务完成率</div>
        <div class="stat-value">{{ monitoringData.taskCompletionRate }}%</div>
        <div class="circular-progress">
          <svg width="60" height="60" viewBox="0 0 120 120">
            <circle
              cx="60"
              cy="60"
              r="54"
              fill="none"
              stroke="#0a2e50"
              stroke-width="12"
            />
            <circle
              cx="60"
              cy="60"
              r="54"
              fill="none"
              stroke="#1890ff"
              stroke-width="12"
              stroke-linecap="round"
              :stroke-dasharray="339.292"
              :stroke-dashoffset="339.292 * (1 - monitoringData.taskCompletionRate / 100)"
              class="progress-circle"
            />
          </svg>
        </div>
      </div>
    </div>
    
    <!-- 第二行：警告信息 -->
    <div class="warning-message" v-if="monitoringData.warningMessage">
      <i class="warning-icon">⚠</i>
      <span>{{ monitoringData.warningMessage }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件属性
const props = defineProps({
  monitoringData: {
    type: Object,
    required: true
  }
});
</script>

<style lang="less" scoped>
.status-cards {
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.primary-stats {
  display: flex;
  gap: 15px;
  
  @media screen and (max-width: 768px) {
    flex-direction: column;
  }
}

.stat-card {
  flex: 1;
  background: rgba(0, 21, 41, 0.7);
  border-radius: 8px;
  padding: 15px;
  position: relative;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(24, 144, 255, 0.2);
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(to right, rgba(24, 144, 255, 0.5), rgba(24, 144, 255, 1));
  }
}

.stat-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.65);
  margin-bottom: 8px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 8px;
  
  .unit {
    font-size: 14px;
    opacity: 0.7;
    margin-left: 4px;
  }
}

.stat-description {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.65);
}

.stat-icon {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 28px;
  opacity: 0.2;
}

.progress-bar {
  height: 6px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  margin: 10px 0;
  overflow: hidden;
  
  .progress-inner {
    height: 100%;
    background: linear-gradient(to right, #1890ff, #52c41a);
    border-radius: 3px;
    transition: width 0.3s ease;
  }
}

.circular-progress {
  position: absolute;
  top: 10px;
  right: 10px;
  transform: rotate(-90deg);
  
  .progress-circle {
    transition: stroke-dashoffset 0.3s ease;
  }
}

.warning-message {
  background: rgba(245, 34, 45, 0.1);
  border: 1px solid rgba(245, 34, 45, 0.2);
  border-radius: 6px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  
  .warning-icon {
    color: #f5222d;
    margin-right: 10px;
    font-size: 18px;
  }
}
</style> 