<template>
  <div class="monitor-area">
    <div class="time-display">{{ currentTime }}</div>
    
    <div class="monitor-grid">
      <div 
        v-for="(camera, index) in ['camera1', 'camera2', 'camera3']" 
        :key="camera"
        class="monitor-item"
      >
        <div class="monitor-header">
          <div class="monitor-title">
            {{ getCameraLabel(camera, index + 1) }}
          </div>
          <el-select 
            v-model="selectedCameras[camera]" 
            size="small" 
            @change="(value) => handleCameraChange(camera, value)"
          >
            <el-option 
              v-for="option in cameraOptions" 
              :key="option.value" 
              :label="option.label" 
              :value="option.value" 
            />
          </el-select>
        </div>
        <div class="monitor-screen">
          <div class="monitor-placeholder">
            <span class="placeholder-icon">📹</span>
            <span class="placeholder-text">实时监控画面</span>
          </div>
        </div>
      </div>
    </div>
    
    <div class="control-panel">
      <div class="panel-title">监控控制面板</div>
      <div class="control-buttons">
        <div class="control-button">
          <i class="control-icon">▶</i>
          <span>播放</span>
        </div>
        <div class="control-button">
          <i class="control-icon">⏸</i>
          <span>暂停</span>
        </div>
        <div class="control-button">
          <i class="control-icon">⏺</i>
          <span>录制</span>
        </div>
        <div class="control-button">
          <i class="control-icon">⏏</i>
          <span>截图</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件属性
const props = defineProps({
  cameraOptions: {
    type: Array,
    required: true
  },
  selectedCameras: {
    type: Object,
    required: true
  },
  currentTime: {
    type: String,
    required: true
  }
});

// 定义组件事件
const emit = defineEmits(['camera-change']);

// 处理相机变更
const handleCameraChange = (cameraId: string, value: string) => {
  emit('camera-change', cameraId, value);
};

// 获取相机标签
const getCameraLabel = (cameraId: string, defaultIndex: number): string => {
  const selectedOption = props.cameraOptions.find(option => option.value === props.selectedCameras[cameraId]);
  return selectedOption ? selectedOption.label : `监控 ${defaultIndex}`;
};
</script>

<style lang="less" scoped>
.monitor-area {
  width: 280px;
  padding: 0 0 0 15px;
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  @media screen and (max-width: 1200px) {
    position: static;
    width: 100%;
    padding: 15px 0 0 0;
  }
}

.time-display {
  font-size: 24px;
  font-weight: 200;
  text-align: center;
  margin: 15px 0;
  font-family: 'Helvetica Neue', Helvetica, sans-serif;
  letter-spacing: 2px;
  color: rgba(255, 255, 255, 0.85);
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
}

.monitor-grid {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 15px;
  flex: 1;
}

.monitor-item {
  background: rgba(0, 21, 41, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(24, 144, 255, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(24, 144, 255, 0.2);
}

.monitor-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
}

.monitor-screen {
  aspect-ratio: 16 / 9;
  background: #000;
  position: relative;
  overflow: hidden;
}

.monitor-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: rgba(255, 255, 255, 0.3);
}

.placeholder-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.placeholder-text {
  font-size: 14px;
}

.control-panel {
  background: rgba(0, 21, 41, 0.7);
  border-radius: 8px;
  border: 1px solid rgba(24, 144, 255, 0.2);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  padding: 12px;
  margin-bottom: 15px;
}

.panel-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 12px;
}

.control-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.control-button {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(24, 144, 255, 0.2);
  border-radius: 4px;
  padding: 8px 0;
  flex: 1;
  min-width: 60px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(24, 144, 255, 0.1);
    border-color: rgba(24, 144, 255, 0.3);
  }
  
  .control-icon {
    display: block;
    font-size: 16px;
    margin-bottom: 4px;
    font-style: normal;
  }
  
  span {
    font-size: 12px;
  }
}

:deep(.el-select) {
  width: 120px;
}

:deep(.el-input__inner) {
  background: rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(24, 144, 255, 0.3) !important;
  color: white !important;
}

:deep(.el-input__suffix) {
  color: rgba(255, 255, 255, 0.5) !important;
}
</style> 