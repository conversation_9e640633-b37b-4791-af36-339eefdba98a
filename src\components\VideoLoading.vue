<template>
  <div class="video-loading">
    <div class="loading-spinner">
      <div class="spinner-circle"></div>
    </div>
    <div class="loading-text">
      <slot>视频流加载中...</slot>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'VideoLoading'
});
</script>

<style scoped>
.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 21, 41, 0.8);
  color: white;
  z-index: 5;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 15px;
  position: relative;
}

.spinner-circle {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #1890ff;
  border-right-color: #1890ff;
  animation: spin 1.2s linear infinite;
}

.spinner-circle:after {
  content: "";
  position: absolute;
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #52c41a;
  animation: spin 1.8s linear infinite;
}

.loading-text {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style> 