<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增巡逻路线' : '修改巡逻路线'" :close-on-click-modal="false" :close-on-press-escape="false" width="65%">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="路线名称" prop="routeName">
            <el-input v-model="dataForm.routeName" placeholder="请输入路线名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="路线描述" prop="routeDes">
            <el-input v-model="dataForm.routeDes" placeholder="请输入作业路线描述"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="路线规划" prop="route">
            <div class="route-map-container">
              <div id="mapContainer" class="map-container"></div>
              <div class="map-controls">
                <el-switch
                  v-model="trafficLayerVisible"
                  active-text="实时交通"
                  @change="toggleTrafficLayer"
                />
                <el-button size="small" type="primary" @click="startDrawRoute">
                  开始绘制路线
                </el-button>
                <el-button size="small" v-if="isDrawing" type="danger" @click="endDrawRoute">
                  完成绘制
                </el-button>
                <el-button size="small" type="warning" @click="clearRoute" :disabled="!dataForm.route">
                  清除路线
                </el-button>
              </div>
              
              <!-- 路线信息区域 -->
              <div class="route-info" v-if="routeInfo.pointCount > 0">
                <div class="route-info-title">路线信息</div>
                <div class="route-info-item">
                  <span>路线点数：</span>
                  <span>{{ routeInfo.pointCount }}</span>
                </div>
                <div class="route-info-item">
                  <span>路线长度：</span>
                  <span>{{ routeInfo.distance }}</span>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, onBeforeUnmount, nextTick, watch } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import AMapLoader from '@amap/amap-jsapi-loader';

// 修复AMap类型声明
declare global {
  interface Window {
    _AMapSecurityConfig: {
      securityJsCode: string;
    };
    AMap: any;
  }
}

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

// 地图相关变量
const map = ref<any>(null);
const trafficLayer = ref<any>(null);
const trafficLayerVisible = ref(false);
const mouseTool = ref<any>(null);
const isDrawing = ref(false);
const routePath = ref<any[]>([]);

// 路线信息状态
const routeInfo = reactive({
  pointCount: 0,
  distance: '0米',
});

const dataForm = reactive({
  routeName: '',
  routeDes: '',
  route: '',
  creatTime: new Date(),
  updateTime: ''
});

const rules = ref({
  routeName: [
    { required: true, message: '路线名称不能为空', trigger: 'blur' }
  ],
  routeDes: [
    { required: true, message: '路线描述不能为空', trigger: 'blur' }
  ],
  route: [
    { required: true, message: '请绘制巡逻路线', trigger: 'change' }
  ],
  creatTime: [
    { required: true, message: '创建时间不能为空', trigger: 'blur' }
  ]
});

// 添加visible监听，确保对话框打开时加载地图
const visibleChanged = async (newVal: boolean) => {
  if (newVal && !map.value) {
    // 延迟一点点，确保DOM已渲染
    setTimeout(async () => {
      await initMap();
      
      // 如果是编辑模式且有路线数据，初始化路线
      if (dataForm.id && dataForm.route) {
        initRouteData();
      }
    }, 200);
  }
};

// 监听visible变化
watch(visible, visibleChanged);

// 初始化地图
const initMap = async () => {
  try {
    // 安全配置
    window._AMapSecurityConfig = {
      securityJsCode: "a9676a96467adef5269a629f780bdb07"
    };
    
    const AMap = await AMapLoader.load({
      key: "eea9447f9cd0ad60cd7a7b612974f3fa", // API密钥
      version: "2.0",
      plugins: [
        "AMap.Scale", 
        "AMap.MouseTool", 
        "AMap.ToolBar",
        "AMap.TileLayer.Traffic",
        "AMap.Traffic"
      ]
    });
    
    // 保存AMap到全局，方便后续使用
    window.AMap = AMap;
    
    // 创建地图实例
    map.value = new AMap.Map("mapContainer", {
      viewMode: "3D",
      zoom: 15,
      center: [104.002052, 30.713956], // 默认中心点坐标
      // mapStyle: 'amap://styles/darkblue' // 暗黑主题
    });
    
    // 添加控件
    if (map.value) {
      map.value.addControl(new AMap.Scale());
      map.value.addControl(new AMap.ToolBar());
    }
    
    // 尝试创建交通图层
    try {
      trafficLayer.value = new AMap.TileLayer.Traffic({
        zIndex: 10,
        opacity: 0.8
      });
    } catch (e) {
      console.warn("方法1创建交通图层失败:", e);
      
      try {
        trafficLayer.value = new AMap.Traffic({
          zIndex: 10
        });
      } catch (e2) {
        console.warn("方法2创建交通图层失败:", e2);
        trafficLayer.value = null;
        ElMessage.warning("交通图层功能不可用");
      }
    }
    
    // 创建鼠标工具
    mouseTool.value = new AMap.MouseTool(map.value);
    
    // 监听绘制完成事件
    if (mouseTool.value) {
      mouseTool.value.on('draw', function(e: any) {
        if (e && e.obj && typeof e.obj.getPath === 'function') {
          // 获取绘制的路径点
          const path = e.obj.getPath();
          
          // 格式化为标准经纬度数组
          const formattedPath = path.map((point: any) => {
            return {
              lng: point.getLng(),
              lat: point.getLat()
            };
          });
          
          // 保存到组件状态
          routePath.value = formattedPath;
          
          // 计算路线长度
          const distance = calculateRouteDistance(formattedPath);
          
          // 更新路线信息
          routeInfo.pointCount = formattedPath.length;
          routeInfo.distance = distance;
          
          // 将路线数据转换为GeoJSON格式并保存到表单
          dataForm.route = JSON.stringify({
            type: "Feature",
            geometry: {
              type: "LineString",
              coordinates: formattedPath.map((p: any) => [p.lng, p.lat])
            },
            properties: {
              name: dataForm.routeName,
              description: dataForm.routeDes
            },
            id: "line"
          });
          
          isDrawing.value = false;
          
          // 显示保存成功和路线点数提示
          ElMessage({
            message: `路线规划完成，共设置了${formattedPath.length}个路线点，长度约${distance}`,
            type: 'success',
            duration: 2000
          });
        }
      });
    }
    
    return true;
  } catch (error) {
    console.error("地图初始化失败:", error);
    ElMessage.error("地图加载失败，请刷新重试");
    return false;
  }
};

// 切换实时交通图层
const toggleTrafficLayer = () => {
  if (!map.value || !trafficLayer.value) return;
  
  try {
    if (trafficLayerVisible.value) {
      // 显示交通图层
      if (!trafficLayer.value.getMap()) {
        trafficLayer.value.setMap(map.value);
      }
      ElMessage({
        message: '已开启实时交通图层',
        type: 'success',
        duration: 1500
      });
    } else {
      // 隐藏交通图层
      trafficLayer.value.setMap(null);
      ElMessage({
        message: '已关闭实时交通图层',
        type: 'info',
        duration: 1500
      });
    }
  } catch (error) {
    console.error("切换交通图层失败:", error);
    ElMessage.error("交通图层操作失败");
    // 重置开关状态
    trafficLayerVisible.value = !trafficLayerVisible.value;
  }
};

// 开始绘制路线
const startDrawRoute = () => {
  if (!mouseTool.value || !map.value) return;
  
  // 清除已有路线
  map.value.clearMap();
  
  // 开始绘制折线
  mouseTool.value.polyline({
    strokeColor: '#28F',
    strokeOpacity: 0.8,
    strokeWeight: 6,
    strokeStyle: "solid",
    showDir: true
  });
  
  isDrawing.value = true;
  
  ElMessage({
    message: '请在地图上点击绘制路线，双击结束',
    type: 'info',
    duration: 3000
  });
};

// 结束绘制路线
const endDrawRoute = () => {
  if (!mouseTool.value) return;
  if (typeof mouseTool.value.close === 'function') {
    mouseTool.value.close(true);
  }
  isDrawing.value = false;
};

// 清除路线
const clearRoute = () => {
  if (!map.value) return;
  
  // 清除地图上的覆盖物
  map.value.clearMap();
  
  // 清除路线数据
  routePath.value = [];
  dataForm.route = '';
  
  // 重置路线信息
  routeInfo.pointCount = 0;
  routeInfo.distance = '0米';
  
  ElMessage({
    message: '已清除路线规划',
    type: 'info',
    duration: 1500
  });
};

// 初始化已有路线数据
const initRouteData = () => {
  if (!map.value || !dataForm.route) return;
  
  try {
    const routeData = JSON.parse(dataForm.route);
    console.log(routeData);
    let pathCoordinates: any[] = [];
    
    // 支持三种数据格式：
    // 1. 标准GeoJSON Feature格式
    if (routeData.type === 'Feature' && routeData.geometry && routeData.geometry.coordinates) {
      pathCoordinates = routeData.geometry.coordinates;
    } 
    // 2. 简化的GeoJSON格式（直接包含coordinates数组）
    else if (routeData.coordinates && Array.isArray(routeData.coordinates)) {
      pathCoordinates = routeData.coordinates;
    } 
    // 3. 旧格式 - 直接的坐标数组
    else if (Array.isArray(routeData)) {
      pathCoordinates = routeData;
    } else {
      throw new Error('无效的路线数据格式');
    }
    
    if (pathCoordinates.length > 0) {
      // 创建折线
      const AMap = window.AMap;
      const polyline = new AMap.Polyline({
        path: pathCoordinates,
        strokeColor: '#28F',
        strokeOpacity: 0.8,
        strokeWeight: 6,
        showDir: true
      });
      
      // 显示路线点位标记
      if (pathCoordinates.length > 1) {
        // 标记起点和终点
        const startMarker = new AMap.Marker({
          position: pathCoordinates[0],
          content: '<div class="route-point start-point">起</div>',
          offset: new AMap.Pixel(-10, -10)
        });
        
        const endMarker = new AMap.Marker({
          position: pathCoordinates[pathCoordinates.length - 1],
          content: '<div class="route-point end-point">终</div>',
          offset: new AMap.Pixel(-10, -10)
        });
        
        map.value.add([polyline, startMarker, endMarker]);
      } else {
        map.value.add(polyline);
      }
      
      map.value.setFitView();
      
      // 更新routePath为最新数据并转换为对象格式
      routePath.value = pathCoordinates.map((p: any) => ({lng: p[0], lat: p[1]}));
      
      // 更新路线信息
      const formattedPath = routePath.value;
      routeInfo.pointCount = formattedPath.length;
      routeInfo.distance = calculateRouteDistance(formattedPath);
      
      ElMessage({
        message: `已加载路线，共${pathCoordinates.length}个路线点`,
        type: 'success',
        duration: 1500
      });
    }
  } catch (error) {
    console.error("路线数据解析失败:", error);
    ElMessage.error("路线数据格式错误，无法显示");
  }
};

// 计算路线长度的函数
const calculateRouteDistance = (path: any[]): string => {
  if (!window.AMap || path.length < 2) return '0米';
  
  try {
    const len = path.length;
    let distance = 0;
    
    for (let i = 0; i < len - 1; i++) {
      const p1 = path[i];
      const p2 = path[i + 1];
      
      // 尝试使用两种方式计算距离
      try {
        if (typeof window.AMap.GeometryUtil?.distance === 'function') {
          // 使用GeometryUtil计算精确距离
          const segmentDist = window.AMap.GeometryUtil.distance(
            [p1.lng || p1[0], p1.lat || p1[1]], 
            [p2.lng || p2[0], p2.lat || p2[1]]
          );
          distance += segmentDist;
        } else {
          // 备用方案：使用简单的球面距离计算
          const lnglat1 = new window.AMap.LngLat(p1.lng || p1[0], p1.lat || p1[1]);
          const lnglat2 = new window.AMap.LngLat(p2.lng || p2[0], p2.lat || p2[1]);
          if (typeof lnglat1.distance === 'function') {
            distance += lnglat1.distance(lnglat2);
          }
        }
      } catch (e) {
        console.warn("距离计算出错，使用简单估算", e);
        // 简单估算 (乘以111km，地球上每经纬度大约111km)
        const dx = (p2.lng || p2[0]) - (p1.lng || p1[0]);
        const dy = (p2.lat || p2[1]) - (p1.lat || p1[1]);
        distance += Math.sqrt(dx * dx + dy * dy) * 111000;
      }
    }
    
    // 格式化距离显示
    if (distance > 1000) {
      return `${(distance / 1000).toFixed(2)}公里`;
    } else {
      return `${Math.round(distance)}米`;
    }
  } catch (error) {
    console.error('计算路线长度失败:', error);
    return '计算错误';
  }
};

const init = (routeId?: number) => {
  visible.value = true;
  dataForm.routeId = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  
  // 重置路线数据
  dataForm.routeName = "";
  dataForm.routeDes = "";
  dataForm.route = "";
  dataForm.creatTime = new Date();
  dataForm.updateTime = "";
  
  // 重置路线信息
  routeInfo.pointCount = 0;
  routeInfo.distance = '0米';

  // 根据ID获取数据
  if (routeId) {
    getInfo(routeId);
  }
  
  // 确保地图在对话框显示后加载
  nextTick(() => {
    visibleChanged(true);
  });
};

// 获取信息
const getInfo = (routeId: number) => {
  baseService.get("/task/patrolroute/" + routeId).then((res) => {
    Object.assign(dataForm, res.data);
    
    // 在数据加载完成后加载路线
    nextTick(() => {
      if (map.value && dataForm.route) {
        initRouteData();
      }
    });
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    
    // 如果已绘制路线但routePath还没有转换为GeoJSON格式
    if (routePath.value.length > 0 && (!dataForm.route || !dataForm.route.includes('Feature'))) {
      dataForm.route = JSON.stringify({
        type: "Feature",
        geometry: {
          type: "LineString",
          coordinates: routePath.value.map((p: any) => [p.lng, p.lat])
        },
        properties: {
          name: dataForm.routeName,
          description: dataForm.routeDes
        },
        id: "line"
      });
    }
    
    // 添加更新时间
    dataForm.updateTime = new Date();
    
    (!dataForm.id ? baseService.post : baseService.put)("/task/patrolroute", dataForm).then((res) => {
      ElMessage.success({
        message: `${!dataForm.id ? '新增' : '修改'}路线成功`,
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

// 清理地图资源
onBeforeUnmount(() => {
  if (map.value && typeof map.value.destroy === 'function') {
    map.value.destroy();
  }
});

defineExpose({
  init
});
</script>

<style lang="less" scoped>
.route-map-container {
  width: 100%;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;

  .map-container {
    width: 100%;
    height: 100%;
  }

  .map-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    background: rgba(0, 0,0, 0.7);
    padding: 10px;
    border-radius: 4px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    max-width: 280px;
  }
}

// 添加路线点标记样式
:deep(.route-point) {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.4);
}

:deep(.start-point) {
  background-color: #1bbc9b;
}

:deep(.end-point) {
  background-color: #e74c3c;
}

// 添加路线规划的预览信息样式
.route-info {
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 100;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  max-width: 200px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  .route-info-title {
    font-weight: bold;
    margin-bottom: 2px;
  }
  
  .route-info-item {
    display: flex;
    justify-content: space-between;
  }
}
</style>
