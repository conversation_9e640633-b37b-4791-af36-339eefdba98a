<template>
  <div class="vehicle-info-window">
    <!-- 标题栏 -->
    <div class="title-bar">
      <span class="title-icon">🚗</span>
      <span class="title-text">车辆 {{ vehicle.id }}</span>
      <span class="status-badge" :class="statusClass">{{ statusText }}</span>
    </div>
    
    <!-- 分隔线 -->
    <div class="decoration-line"></div>
    
    <!-- 电量信息 -->
    <div class="info-row battery-row">
      <span class="info-label">电量:</span>
      <div class="progress-container">
        <div class="progress-bar" :class="batteryClass" :style="{ width: `${vehicle.battery}%` }"></div>
      </div>
      <span class="battery-text" :class="{ 'low-battery': vehicle.battery <= 20 }">{{ vehicle.battery }}%</span>
    </div>
    
    <!-- 速度信息 -->
    <div class="info-row">
      <span class="info-label">速度:</span>
      <span class="info-value">{{ vehicle.speed }} km/h</span>
    </div>
    
    <!-- 位置信息 -->
    <div class="info-row">
      <span class="info-label">位置:</span>
      <span class="info-value">{{ vehicle.location }}</span>
    </div>
    
    <!-- 按钮区域 -->
    <div class="button-row">
      <div class="action-button detail-button" @click="handleDetailClick">查看详情</div>
      <div class="action-button track-button" @click="handleTrackClick">轨迹回放</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 定义组件属性
const props = defineProps({
  vehicle: {
    type: Object,
    required: true
  }
});

// 定义事件
const emit = defineEmits(['detail-click', 'track-click']);

// 计算状态文本
const statusText = computed(() => {
  switch (props.vehicle.status) {
    case 'online': return '在线';
    case 'charging': return '充电中';
    case 'offline': return '离线';
    default: return '未知';
  }
});

// 计算状态样式
const statusClass = computed(() => {
  switch (props.vehicle.status) {
    case 'online': return 'status-online';
    case 'charging': return 'status-charging';
    case 'offline': return 'status-offline';
    default: return '';
  }
});

// 计算电量样式
const batteryClass = computed(() => {
  if (props.vehicle.battery <= 20) return 'battery-low';
  if (props.vehicle.battery <= 50) return 'battery-medium';
  return 'battery-high';
});

// 按钮点击处理
const handleDetailClick = () => {
  emit('detail-click', props.vehicle);
};

const handleTrackClick = () => {
  emit('track-click', props.vehicle);
};
</script>

<style lang="less" scoped>
.vehicle-info-window {
  width: 250px;
  background: linear-gradient(to bottom, #1a3a5f, #001529);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  padding: 12px;
  color: white;
  font-family: 'Microsoft YaHei', sans-serif;
  border: 1px solid rgba(24, 144, 255, 0.3);
  animation: fadeIn 0.3s ease-out forwards;
}

.title-bar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.title-icon {
  margin-right: 8px;
  font-size: 18px;
}

.title-text {
  font-weight: bold;
  font-size: 16px;
  flex: 1;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.status-online {
  background-color: rgba(82, 196, 26, 0.2);
  color: #52c41a;
  border: 1px solid rgba(82, 196, 26, 0.3);
}

.status-charging {
  background-color: rgba(250, 173, 20, 0.2);
  color: #faad14;
  border: 1px solid rgba(250, 173, 20, 0.3);
}

.status-offline {
  background-color: rgba(245, 34, 45, 0.2);
  color: #f5222d;
  border: 1px solid rgba(245, 34, 45, 0.3);
}

.decoration-line {
  height: 2px;
  width: 100%;
  background: linear-gradient(to right, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.8), rgba(24, 144, 255, 0.1));
  margin-bottom: 12px;
}

.info-row {
  margin-bottom: 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.info-label {
  margin-right: 8px;
}

.battery-row {
  display: flex;
  align-items: center;
}

.progress-container {
  flex: 1;
  height: 12px;
  background-color: rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid rgba(24, 144, 255, 0.3);
}

.progress-bar {
  height: 100%;
}

.battery-low {
  background: linear-gradient(to right, #f5222d, #ff7875);
  animation: pulse 1.5s infinite;
}

.battery-medium {
  background: linear-gradient(to right, #faad14, #ffc53d);
}

.battery-high {
  background: linear-gradient(to right, #52c41a, #95de64);
}

.battery-text {
  margin-left: 8px;
}

.low-battery {
  color: #ff7875;
}

.button-row {
  display: flex;
  justify-content: space-between;
  margin-top: 12px;
}

.action-button {
  padding: 6px 12px;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  flex: 1;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.detail-button {
  background-color: rgba(24, 144, 255, 0.8);
  margin-right: 8px;
  
  &:hover {
    background-color: rgba(24, 144, 255, 1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  }
}

.track-button {
  background-color: rgba(0, 0, 0, 0.2);
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.4);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}
</style> 