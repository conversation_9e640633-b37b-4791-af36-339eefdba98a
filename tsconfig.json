{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "noImplicitThis": true, "jsx": "preserve", "allowJs": true, "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "resolveJsonModule": true, "baseUrl": ".", "types": ["vite/client"], "paths": {"@/*": ["src/*"]}, "compilerOptions": {"types": ["vite-plugin-svg-icons/client"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.vue", "tests/**/*.ts"], "exclude": ["node_modules"]}