<template>
  <div class="video-management">
    <!-- 页面标题和操作按钮 -->
    <div class="header">
      <h2>视频管理</h2>
      <div class="actions">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索视频"
          style="width: 300px; margin-right: 10px"
          @keyup.enter="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
        <el-button type="primary" @click="showUploadDialog">
          <el-icon><Plus /></el-icon>
          上传视频
        </el-button>
      </div>
    </div>

    <!-- 视频列表 -->
    <div class="video-list">
      <el-table
        :data="videoList"
        v-loading="loading"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column label="缩略图" width="120">
          <template #default="{ row }">
            <div class="thumbnail-container">
              <img
                :src="row.thumbnailUrl || '/default-video-thumb.jpg'"
                :alt="row.title"
                class="thumbnail"
                @click="previewVideo(row)"
              />
              <div class="play-overlay" @click="previewVideo(row)">
                <el-icon><VideoPlay /></el-icon>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="title" label="标题" width="200" show-overflow-tooltip />

        <el-table-column
          prop="description"
          label="描述"
          min-width="200"
          show-overflow-tooltip
        />

        <el-table-column prop="duration" label="时长" width="100">
          <template #default="{ row }">
            {{ formatDuration(row.duration) }}
          </template>
        </el-table-column>

        <el-table-column prop="fileSize" label="文件大小" width="100">
          <template #default="{ row }">
            {{ formatFileSize(row.fileSize) }}
          </template>
        </el-table-column>

        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="uploadTime" label="上传时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.uploadTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="previewVideo(row)">
              预览
            </el-button>
            <el-button type="danger" size="small" @click="deleteVideo(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedVideos.length > 0" class="batch-actions">
        <span>已选择 {{ selectedVideos.length }} 个视频</span>
        <el-button type="danger" @click="batchDelete"> 批量删除 </el-button>
      </div>
    </div>

    <!-- 上传对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传视频"
      width="600px"
      :before-close="handleUploadClose"
    >
      <el-form
        :model="uploadForm"
        :rules="uploadRules"
        ref="uploadFormRef"
        label-width="100px"
      >
        <el-form-item label="视频标题" prop="title">
          <el-input v-model="uploadForm.title" placeholder="请输入视频标题" />
        </el-form-item>

        <el-form-item label="视频描述" prop="description">
          <el-input
            v-model="uploadForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入视频描述"
          />
        </el-form-item>

        <el-form-item label="选择文件" prop="file">
          <el-upload
            class="upload-demo"
            ref="uploadRef"
            :auto-upload="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            :on-remove="handleFileRemove"
            accept="video/*"
            :limit="1"
            drag
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <template #tip>
              <div class="el-upload__tip">
                支持 mp4、avi、mov、wmv 格式，文件大小不超过 500MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <!-- 上传进度 -->
        <el-form-item v-if="uploadProgress > 0">
          <el-progress :percentage="uploadProgress" :status="uploadStatus" />
          <div class="upload-info">
            <span>{{ uploadInfo }}</span>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleUploadClose">取消</el-button>
          <el-button
            type="primary"
            :loading="uploading"
            :disabled="!uploadForm.file"
            @click="handleUpload"
          >
            {{ uploading ? "上传中..." : "开始上传" }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 视频预览对话框 -->
    <el-dialog
      v-model="previewDialogVisible"
      :title="previewVideo?.title || '视频预览'"
      width="800px"
      :before-close="handlePreviewClose"
    >
      <div class="video-preview">
        <video
          ref="videoPlayerRef"
          :src="previewVideoUrl"
          controls
          preload="metadata"
          style="width: 100%; height: 400px; background: #000"
          @loadedmetadata="onVideoLoaded"
          @timeupdate="onTimeUpdate"
          @ended="onVideoEnded"
        >
          您的浏览器不支持视频播放。
        </video>

        <!-- 视频信息 -->
        <div class="video-info" v-if="currentPreviewVideo">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="标题">
              {{ currentPreviewVideo.title }}
            </el-descriptions-item>
            <el-descriptions-item label="时长">
              {{ formatDuration(currentPreviewVideo.duration) }}
            </el-descriptions-item>
            <el-descriptions-item label="文件大小">
              {{ formatFileSize(currentPreviewVideo.fileSize) }}
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(currentPreviewVideo.status)">
                {{ getStatusText(currentPreviewVideo.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="上传时间" :span="2">
              {{ formatDateTime(currentPreviewVideo.uploadTime) }}
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="2">
              {{ currentPreviewVideo.description }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handlePreviewClose">关闭</el-button>
          <el-button type="primary" @click="downloadVideo"> 下载视频 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Search, Plus, VideoPlay, UploadFilled } from "@element-plus/icons-vue";
import { videoApi } from "@/api/video";
import type { Video } from "@/types";

// 响应式数据
const loading = ref(false);
const videoList = ref<Video[]>([]);
const selectedVideos = ref<Video[]>([]);
const searchKeyword = ref("");
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

// 上传相关
const uploadDialogVisible = ref(false);
const uploadFormRef = ref();
const uploadRef = ref();
const uploading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref<"success" | "exception" | undefined>();
const uploadInfo = ref("");

const uploadForm = reactive({
  title: "",
  description: "",
  file: null as File | null,
});

const uploadRules = {
  title: [
    { required: true, message: "请输入视频标题", trigger: "blur" },
    { min: 2, max: 50, message: "标题长度在 2 到 50 个字符", trigger: "blur" },
  ],
  description: [
    { required: true, message: "请输入视频描述", trigger: "blur" },
    { min: 5, max: 200, message: "描述长度在 5 到 200 个字符", trigger: "blur" },
  ],
  file: [{ required: true, message: "请选择视频文件", trigger: "change" }],
};

// 预览相关
const previewDialogVisible = ref(false);
const currentPreviewVideo = ref<Video | null>(null);
const videoPlayerRef = ref<HTMLVideoElement>();
const previewVideoUrl = computed(() => {
  return currentPreviewVideo.value?.fileUrl || "";
});

// 生命周期
onMounted(() => {
  loadVideoList();
});

// 方法
const loadVideoList = async () => {
  loading.value = true;
  try {
    const response = await videoApi.getVideoList({
      page: currentPage.value,
      size: pageSize.value,
      keyword: searchKeyword.value,
    });

    videoList.value = response.data.data;
    total.value = response.data.total;
  } catch (error) {
    ElMessage.error("加载视频列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  loadVideoList();
};

const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadVideoList();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadVideoList();
};

const handleSelectionChange = (videos: Video[]) => {
  selectedVideos.value = videos;
};

// 上传相关方法
const showUploadDialog = () => {
  uploadDialogVisible.value = true;
};

const handleFileChange = (file: any) => {
  uploadForm.file = file.raw;
  // 自动填充文件名作为标题
  if (!uploadForm.title) {
    const fileName = file.name;
    const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf("."));
    uploadForm.title = nameWithoutExt;
  }
};

const handleFileRemove = () => {
  uploadForm.file = null;
};

const beforeUpload = (file: File) => {
  const isVideo = file.type.startsWith("video/");
  const isLt500M = file.size / 1024 / 1024 < 500;

  if (!isVideo) {
    ElMessage.error("只能上传视频文件!");
    return false;
  }

  if (!isLt500M) {
    ElMessage.error("文件大小不能超过 500MB!");
    return false;
  }

  return true;
};

const handleUpload = async () => {
  if (!uploadFormRef.value) return;

  try {
    await uploadFormRef.value.validate();

    uploading.value = true;
    uploadProgress.value = 0;
    uploadStatus.value = undefined;
    uploadInfo.value = "准备上传...";

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10;
        if (uploadProgress.value < 30) {
          uploadInfo.value = "正在上传文件...";
        } else if (uploadProgress.value < 60) {
          uploadInfo.value = "文件上传中...";
        } else if (uploadProgress.value < 90) {
          uploadInfo.value = "正在处理视频...";
        }
      }
    }, 500);

    const response = await videoApi.uploadVideo({
      file: uploadForm.file!,
      title: uploadForm.title,
      description: uploadForm.description,
    });

    clearInterval(progressInterval);
    uploadProgress.value = 100;
    uploadStatus.value = "success";
    uploadInfo.value = "上传成功！";

    ElMessage.success("视频上传成功！");

    // 刷新列表
    await loadVideoList();

    // 关闭对话框
    setTimeout(() => {
      handleUploadClose();
    }, 1000);
  } catch (error: any) {
    uploadStatus.value = "exception";
    uploadInfo.value = "上传失败：" + (error.message || "未知错误");
    ElMessage.error("上传失败，请重试");
  } finally {
    uploading.value = false;
  }
};

const handleUploadClose = () => {
  // 重置表单
  uploadForm.title = "";
  uploadForm.description = "";
  uploadForm.file = null;
  uploadProgress.value = 0;
  uploadStatus.value = undefined;
  uploadInfo.value = "";

  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }

  if (uploadFormRef.value) {
    uploadFormRef.value.resetFields();
  }

  uploadDialogVisible.value = false;
};

// 预览相关方法
const previewVideo = (video: Video) => {
  if (video.status !== "READY") {
    ElMessage.warning("视频还在处理中，暂时无法预览");
    return;
  }

  currentPreviewVideo.value = video;
  previewDialogVisible.value = true;
};

const handlePreviewClose = () => {
  // 暂停视频播放
  if (videoPlayerRef.value) {
    videoPlayerRef.value.pause();
  }

  currentPreviewVideo.value = null;
  previewDialogVisible.value = false;
};

const onVideoLoaded = () => {
  console.log("Video loaded");
};

const onTimeUpdate = () => {
  // 可以在这里处理播放进度
};

const onVideoEnded = () => {
  console.log("Video ended");
};

const downloadVideo = () => {
  if (currentPreviewVideo.value) {
    const link = document.createElement("a");
    link.href = currentPreviewVideo.value.fileUrl;
    link.download = currentPreviewVideo.value.title;
    link.click();
  }
};

// 删除相关方法
const deleteVideo = async (video: Video) => {
  try {
    await ElMessageBox.confirm(`确定要删除视频 "${video.title}" 吗？`, "删除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    await videoApi.deleteVideo(video.id);
    ElMessage.success("删除成功");

    // 刷新列表
    await loadVideoList();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除失败");
    }
  }
};

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedVideos.value.length} 个视频吗？`,
      "批量删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }
    );

    // 并发删除
    const deletePromises = selectedVideos.value.map((video) =>
      videoApi.deleteVideo(video.id)
    );

    await Promise.all(deletePromises);
    ElMessage.success("批量删除成功");

    // 清空选择
    selectedVideos.value = [];

    // 刷新列表
    await loadVideoList();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("批量删除失败");
    }
  }
};

// 工具方法
const formatDuration = (seconds: number): string => {
  if (!seconds || seconds <= 0) return "00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours.toString().padStart(2, "0")}:${minutes
      .toString()
      .padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  } else {
    return `${minutes.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
  }
};

const formatFileSize = (bytes: number): string => {
  if (!bytes || bytes <= 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return "";

  const date = new Date(dateTime);
  return date.toLocaleString("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });
};

const getStatusType = (status: string): string => {
  switch (status) {
    case "READY":
      return "success";
    case "PROCESSING":
      return "warning";
    case "ERROR":
      return "danger";
    default:
      return "info";
  }
};

const getStatusText = (status: string): string => {
  switch (status) {
    case "READY":
      return "就绪";
    case "PROCESSING":
      return "处理中";
    case "ERROR":
      return "错误";
    default:
      return "未知";
  }
};
</script>

<style scoped>
.video-management {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.actions {
  display: flex;
  align-items: center;
}

.thumbnail-container {
  position: relative;
  width: 80px;
  height: 60px;
  cursor: pointer;
}

.thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 4px;
}

.play-overlay:hover {
  opacity: 1;
}

.play-overlay .el-icon {
  color: white;
  font-size: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.batch-actions {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.upload-demo {
  width: 100%;
}

.upload-info {
  margin-top: 10px;
  text-align: center;
  color: #606266;
  font-size: 14px;
}

.video-preview {
  margin-top: 20px;
}

.video-info {
  margin-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 15px;
  }

  .actions {
    width: 100%;
    justify-content: space-between;
  }

  .batch-actions {
    flex-direction: column;
    gap: 10px;
  }
}
</style>
