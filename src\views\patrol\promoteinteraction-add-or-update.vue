<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="视频标题" prop="title">
        <el-input v-model="dataForm.title" placeholder="视频标题"></el-input>
      </el-form-item>
          <el-form-item label="视频描述" prop="description">
        <el-input v-model="dataForm.description" placeholder="视频描述"></el-input>
      </el-form-item>
          <el-form-item label="视频时长" prop="duration">
        <el-input v-model="dataForm.duration" placeholder="视频时长"></el-input>
      </el-form-item>
          <el-form-item label="文件大小" prop="fileSize">
        <el-input v-model="dataForm.fileSize" placeholder="文件大小"></el-input>
      </el-form-item>
          <el-form-item label="视频文件路径" prop="filePath">
        <el-input v-model="dataForm.filePath" placeholder="视频文件路径"></el-input>
      </el-form-item>
          <el-form-item label="视频状态" prop="status">
        <el-input v-model="dataForm.status" placeholder="视频状态"></el-input>
      </el-form-item>
          <el-form-item label="上传时间" prop="uploadTime">
        <el-input v-model="dataForm.uploadTime" placeholder="上传时间"></el-input>
      </el-form-item>
          <el-form-item label="创建时间" prop="createdAt">
        <el-input v-model="dataForm.createdAt" placeholder="创建时间"></el-input>
      </el-form-item>
          <el-form-item label="更新时间" prop="updatedAt">
        <el-input v-model="dataForm.updatedAt" placeholder="更新时间"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  title: '',  description: '',  duration: '',  fileSize: '',  filePath: '',  status: '',  uploadTime: '',  createdAt: '',  updatedAt: ''});

const rules = ref({
          title: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          description: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          duration: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          fileSize: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          filePath: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          status: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          uploadTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          createdAt: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          updatedAt: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/task/publicityvideo/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/task/publicityvideo", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
