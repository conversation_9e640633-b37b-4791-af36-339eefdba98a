<template>
  <div class="grid-container">
    <!-- 第一行第一列：视频预览和信息 -->
    <div class="video-preview-info grid-item-1-1">
      <div class="video-preview-block">
        <div class="area-title">视频预览</div>
        <div class="video-content-wrapper">
          <video
            ref="videoPlayer"
            class="video-player"
            controls
            :src="state.currentVideo?.filePath"
          >
            您的浏览器不支持视频播放
          </video>
          <div class="video-info-block" v-if="state.currentVideo">
            <div class="video-title">{{ state.currentVideo.title }}</div>
            <div class="video-description">{{ state.currentVideo.description }}</div>
            <div class="video-meta">
              <div>
                <i class="el-icon-timer"></i> 时长: {{ state.currentVideo.duration }}
              </div>
              <div>
                <i class="el-icon-document"></i> 大小:
                {{ state.currentVideo.fileSize }} MB
              </div>
              <div>
                <i class="el-icon-upload"></i> 上传时间:
                {{ state.currentVideo.uploadTime }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 第一行第二列：巡逻车辆 -->
    <div class="vehicle-card-list-block grid-item-1-2">
      <div class="area-title">
        巡逻车辆
        <el-link type="primary" class="view-all-link" @click="selectAllVehicles"
          >全选</el-link
        >
      </div>
      <div class="vehicle-card-list">
        <div
          v-for="vehicle in state.vehicleList"
          :key="vehicle.id"
          class="vehicle-card"
          :class="{ offline: vehicle.status !== '在线' }"
        >
          <el-checkbox v-model="vehicle.selected" />
          <div class="vehicle-card-content">
            <div class="vehicle-icon-box">
              <i class="el-icon-truck vehicle-icon"></i>
            </div>
            <div class="vehicle-info-box">
              <div class="vehicle-code-row">
                <span class="vehicle-code">{{ vehicle.vehicleCode }}</span>
                <span class="vehicle-dept">{{ vehicle.deptName }}</span>
              </div>
              <div class="vehicle-status-row">
                <el-tag :type="vehicle.status === '在线' ? 'success' : 'info'">{{
                  vehicle.status
                }}</el-tag>
                <span v-if="vehicle.currentVideo" class="current-video"
                  >当前播放: {{ vehicle.currentVideo }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="distribute-btn-container">
        <el-button
          type="primary"
          :disabled="selectedVehicleCount === 0"
          @click="distributeVideos"
        >
          <i class="el-icon-video-camera"></i>分配视频到勾选中的车辆 ({{
            selectedVehicleCount
          }})
        </el-button>
      </div>
    </div>

    <!-- 第二行第一列：视频列表 -->
    <div class="video-list-block grid-item-2-1">
      <div class="video-list-header">
        <div class="area-title">视频列表</div>
        <div class="list-operations">
          <el-input
            placeholder="请输入视频标题进行搜索"
            v-model="state.dataForm.key"
            class="search-input"
            clearable
            @input="state.getDataList()"
          >
            <template #prefix>
              <el-icon><Search /></el-icon> </template
          ></el-input>
          <el-button type="primary" @click="searchVideos" style="margin-left: 5px"
            >搜索视频</el-button
          >
          <el-button type="primary" @click="addOrUpdateHandle()" style="margin-left: 10px"
            >上传视频</el-button
          >
        </div>
      </div>
      <el-table
        v-loading="state.dataListLoading"
        :data="state.dataList"
        border
        @selection-change="state.dataListSelectionChangeHandle"
        @row-click="handleRowClick"
        style="width: 100%"
      >
        <el-table-column type="selection" width="50" align="center"></el-table-column>
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
        ></el-table-column>
        <el-table-column prop="title" label="视频标题" min-width="180" align="center">
          <template #default="{ row }">
            <span class="video-title-cell">
              <i class="el-icon-video-camera"></i> {{ row.title }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="description"
          label="描述"
          min-width="180"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="duration"
          label="时长"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column prop="fileSize" label="文件大小" width="100" align="center">
          <template #default="{ row }"> {{ row.fileSize }} MB </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="uploadTime"
          label="上传时间"
          width="150"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" width="80" align="center" fixed="right">
          <template #default="{ row }">
            <el-button
              type="danger"
              size="small"
              icon="Delete"
              circle
              @click.stop="state.deleteHandle(row.id)"
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          @size-change="state.pageSizeChangeHandle"
          @current-change="state.pageCurrentChangeHandle"
          :current-page="state.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="state.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="state.total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 第二行第二列：视频分配历史记录 -->
    <div class="distribution-history-block grid-item-2-2">
      <div class="area-title">
        视频分配历史记录
        <el-button type="text" size="small" @click="refreshHistory">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      <el-table
        :data="state.distributionHistory"
        border
        style="width: 100%"
        max-height="100%"
      >
        <el-table-column
          prop="videoTitle"
          label="视频"
          min-width="100"
          align="center"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="vehicleCode"
          label="车辆"
          width="80"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="distributeTime"
          label="分配时间"
          width="100"
          align="center"
        ></el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag
              :type="
                row.status === '成功'
                  ? 'success'
                  : row.status === '失败'
                  ? 'danger'
                  : 'warning'
              "
              size="small"
            >
              {{ row.status }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>

  <!-- 弹窗, 新增 / 修改 -->
  <add-or-update
    v-if="state.addOrUpdateVisible"
    ref="addOrUpdateRef"
    @refreshDataList="state.getDataList"
  ></add-or-update>
  <!--  </div>-->
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs, onMounted, computed, watch } from "vue";
import AddOrUpdate from "./publicityvideo-add-or-update.vue";
import { Search } from "@element-plus/icons-vue";
import baseService from "@/service/baseService";
import { ElMessage, ElMessageBox } from "element-plus";

interface VideoItem {
  id: number;
  title: string;
  description: string;
  duration: string;
  fileSize: number;
  filePath: string;
  status: string;
  uploadTime: string;
  createdAt: string;
  updatedAt: string;
}

interface VehicleItem {
  id: number;
  vehicleCode: string;
  deptName: string;
  status: string;
  currentVideo: string | null;
  selected?: boolean;
}

interface DistributionHistory {
  id: number;
  videoTitle: string;
  vehicleCode: string;
  distributeTime: string;
  status: string;
}

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/publicityvideo/page",
  getDataListIsPage: true,
  exportURL: "/task/publicityvideo/export",
  deleteURL: "/task/publicityvideo",
  dataForm: { key: "" }, // 添加dataForm对象，包含搜索关键字
});

const videoPlayer = ref<HTMLVideoElement | null>(null);
const addOrUpdateRef = ref();

const state = reactive({
  ...useView(view),
  ...toRefs(view),
  searchQuery: "",
  currentVideo: null as VideoItem | null,
  addOrUpdateVisible: false,
  vehicleList: [
    {
      id: 1,
      vehicleCode: "A88888",
      deptName: "依维柯",
      status: "在线",
      currentVideo: "宣传视频 1",
      selected: false,
    },
    {
      id: 2,
      vehicleCode: "粤A66666",
      deptName: "福特全顺",
      status: "在线",
      currentVideo: null,
      selected: false,
    },
    {
      id: 3,
      vehicleCode: "粤A99999",
      deptName: "依维柯",
      status: "离线",
      currentVideo: null,
      selected: false,
    },
    {
      id: 4,
      vehicleCode: "A12345",
      deptName: "江铃",
      status: "在线",
      currentVideo: "宣传视频 3",
      selected: false,
    },
    {
      id: 5,
      vehicleCode: "粤A54321",
      deptName: "福特全顺",
      status: "在线",
      currentVideo: null,
      selected: false,
    },
    {
      id: 6,
      vehicleCode: "粤A11111",
      deptName: "依维柯",
      status: "在线",
      currentVideo: null,
      selected: false,
    },
  ] as VehicleItem[],
  selectedVehicles: [] as VehicleItem[],
  dataList: [] as VideoItem[],
  distributionHistory: [] as DistributionHistory[],
});
// 自定义删除方法 - 支持单个删除和批量删除
const deleteHandle = (id?: string | number): Promise<any> => {
  // 确定要删除的ID列表
  let idsToDelete: number[] = [];
  let confirmMessage = "";

  if (id) {
    // 单个删除
    const numericId = typeof id === "string" ? parseInt(id) : id;
    idsToDelete = [numericId];
    confirmMessage = "确定要删除这条记录吗?";
  } else {
    // 批量删除
    idsToDelete = state.dataListSelections?.map((item: any) => item.id) || [];
    if (idsToDelete.length === 0) {
      ElMessage.warning("请选择要删除的记录");
      return Promise.reject("没有选择要删除的记录");
    }
    confirmMessage = `确定要删除选中的 ${idsToDelete.length} 条记录吗?`;
  }

  return ElMessageBox.confirm(confirmMessage, "删除确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 调用删除API - 后端期望接收ID数组
      return baseService
        .delete(view.deleteURL, idsToDelete)
        .then(() => {
          ElMessage.success("删除成功");
          // 清空选择
          if (state.dataListSelections) {
            state.dataListSelections = [];
          }
          // 删除成功后刷新数据
          getDataList();
          return Promise.resolve();
        })
        .catch((error) => {
          console.error("删除失败:", error);
          ElMessage.error("删除失败，请检查网络连接或联系管理员");
          return Promise.reject(error);
        });
    })
    .catch((error) => {
      // 用户取消删除
      console.log("用户取消删除操作");
      return Promise.reject(error);
    });
};

// 自定义获取数据列表方法，覆盖useView中的方法
const getDataList = () => {
  if (!view.getDataListURL) {
    return;
  }

  console.log("开始查询数据，URL:", view.getDataListURL);
  console.log("查询参数:", {
    order: state.order,
    orderField: state.orderField,
    page: view.getDataListIsPage ? state.page : null,
    limit: view.getDataListIsPage ? state.limit : null,
    ...view.dataForm,
  });

  state.dataListLoading = true;

  baseService
    .get(view.getDataListURL, {
      order: state.order,
      orderField: state.orderField,
      page: view.getDataListIsPage ? state.page : null,
      limit: view.getDataListIsPage ? state.limit : null,
      ...view.dataForm,
    })
    .then((res) => {
      state.dataListLoading = false;
      console.log("API返回数据:", res);

      if (res.data && res.data.list) {
        // 格式化数据
        const formattedList = res.data.list.map((item: any) => {
          // 格式化文件大小为MB
          const fileSizeMB = item.fileSize
            ? (item.fileSize / (1024 * 1024)).toFixed(2)
            : 0;

          // 格式化日期
          const formatDate = (dateString: string) => {
            if (!dateString) return "";
            const date = new Date(dateString);
            return date.toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
            });
          };

          return {
            ...item,
            fileSize: Number(fileSizeMB), // 确保转换为数字类型
            uploadTime: formatDate(item.uploadTime),
            createdAt: formatDate(item.createdAt),
            updatedAt: formatDate(item.updatedAt),
          };
        });

        state.dataList = formattedList;
        state.total = res.data.total || 0;
        console.log("格式化后的数据列表:", state.dataList);
        console.log("当前state对象:", state);
      } else {
        state.dataList = [];
        state.total = 0;
        console.warn("API返回数据格式不符合预期:", res.data);
      }
    })
    .catch((error) => {
      state.dataListLoading = false;
      console.error("获取数据列表失败:", error);
    });
};

// 替换原始的getDataList方法
state.getDataList = getDataList;
state.deleteHandle = deleteHandle;

const selectedVehicleCount = computed(
  () => state.vehicleList.filter((v) => v.selected).length
);

onMounted(() => {
  console.log("组件已挂载，准备获取数据");
  // 确保在组件挂载后立即获取数据
  setTimeout(() => {
    console.log("调用getDataList方法");
    getDataList();
  }, 100);

  // 初始化分配历史记录
  state.distributionHistory = [
    {
      id: 1,
      videoTitle: "宣传视频 1",
      vehicleCode: "A88888",
      distributeTime: "07/25 14:30",
      status: "成功",
    },
    {
      id: 2,
      videoTitle: "宣传视频 3",
      vehicleCode: "A12345",
      distributeTime: "07/25 13:20",
      status: "成功",
    },
    {
      id: 3,
      videoTitle: "宣传视频 2",
      vehicleCode: "粤A66666",
      distributeTime: "07/25 11:15",
      status: "失败",
    },
    {
      id: 4,
      videoTitle: "宣传视频 4",
      vehicleCode: "粤A54321",
      distributeTime: "07/24 16:45",
      status: "成功",
    },
    {
      id: 5,
      videoTitle: "宣传视频 1",
      vehicleCode: "粤A11111",
      distributeTime: "07/24 10:30",
      status: "进行中",
    },
  ];
});

const searchVideos = () => {
  state.getDataList();
};

const addOrUpdateHandle = (id?: number) => {
  state.addOrUpdateVisible = true;
  setTimeout(() => {
    addOrUpdateRef.value?.init(id);
  }, 0);
};

const handleRowClick = (row: VideoItem) => {
  state.currentVideo = row;
  if (videoPlayer.value) {
    videoPlayer.value.currentTime = 0;
    videoPlayer.value.play().catch((e: Error) => console.log("视频播放失败:", e));
  }
};

const getStatusType = (status: string): string => {
  switch (status) {
    case "已上传":
      return "success";
    case "处理中":
      return "warning";
    case "可用":
      return "primary";
    default:
      return "info";
  }
};

const selectAllVehicles = () => {
  const allSelected = state.vehicleList.every((v) => v.selected);
  state.vehicleList.forEach((v) => {
    v.selected = !allSelected;
  });
};

const distributeVideos = () => {
  // 实现分配视频到勾选车辆的逻辑
  console.log("分配视频到勾选车辆");
  // 这里可以添加实际的分配逻辑，包括调用API等
};

const refreshHistory = () => {
  // 刷新分配历史记录
  console.log("刷新分配历史记录");
  // 这里可以添加获取最新历史记录的逻辑
};
</script>

<style lang="less" scoped>
/* 网格容器 */
.grid-container {
  display: grid;
  grid-template-columns: minmax(0, 2fr) minmax(320px, 400px);
  grid-template-rows: minmax(400px, 1fr) minmax(350px, 1fr);
  gap: 16px;
  min-height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
}

/* 第一行第一列：视频预览和信息 */
.grid-item-1-1 {
  grid-column: 1;
  grid-row: 1;
  display: flex;
  flex-direction: column;
  gap: 32px;
  min-height: 0;
}

/* 第一行第二列：巡逻车辆 */
.grid-item-1-2 {
  grid-column: 2;
  grid-row: 1;
  min-height: 0;
}

/* 第二行第一列：视频列表 */
.grid-item-2-1 {
  grid-column: 1;
  grid-row: 2;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 第二行第二列：视频分配历史记录 */
.grid-item-2-2 {
  grid-column: 2;
  grid-row: 2;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* 视频预览区域 */
.video-preview-info {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-preview-block {
  flex: 1;
  height: 100%;
}

.vehicle-card-list-block {
  min-height: 0;
}

.video-list-block {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.distribution-history-block {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.video-content-wrapper {
  display: flex;
  gap: 16px;
  flex: 1;
  min-height: 0;
  overflow: hidden;
}

.video-player {
  flex: 1;
  height: 100%;
  min-height: 200px;
  background: #222;
  border-radius: 6px;
  object-fit: contain;
}

.video-info-block {
  flex: 0 0 280px;
  background: #223a5e;
  border-radius: 6px;
  padding: 16px;
  color: #fff;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
}

.video-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
}

.video-description {
  color: #bfcbd9;
  margin-bottom: 12px;
}

.video-meta {
  display: flex;
  flex-direction: column;
  gap: 6px;
  color: #8fa4c8;
  font-size: 14px;
}

/* 车辆列表区域 */
.vehicle-card-list-block {
  min-height: 0;
}

.vehicle-card-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  overflow-y: auto;
  flex: 1;
  min-height: 0;
  padding-right: 4px;

  /* 滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #223a5e;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #4fc08d;
    border-radius: 3px;
  }
}

.vehicle-card {
  display: flex;
  align-items: center;
  background: #223a5e;
  border-radius: 6px;
  padding: 12px;
  gap: 12px;
  color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  flex-shrink: 0;
  border: 1px solid transparent;

  &:hover {
    background: #2a4666;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &.offline {
    opacity: 0.6;
    filter: grayscale(0.3);
  }
}

.vehicle-card-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.vehicle-icon-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #18325a;
  border-radius: 50%;
}

.vehicle-icon {
  font-size: 22px;
  color: #4fc08d;
}

.vehicle-info-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.vehicle-code-row {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
}

.vehicle-dept {
  font-size: 13px;
  color: #8fa4c8;
}

.vehicle-status-row {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 13px;
}

.current-video {
  color: #ffd04b;
  font-size: 13px;
}

.distribute-btn-container {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

/* 视频列表区域 */
.video-list-block {
}

.video-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  flex-shrink: 0;
}

.video-list-header .area-title {
  margin-bottom: 0;
}

.list-operations {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-input {
  width: 300px;
}

.video-list-block .el-table {
  flex: 1;
  min-height: 0;

  .el-table__body-wrapper {
    max-height: none;
    overflow-y: auto;
  }
}

.pagination-container {
  margin-top: 12px;
  display: flex;
  justify-content: center;
  flex-shrink: 0;
}

/* 视频分配历史记录区域 */
.distribution-history-block {
}

.distribution-history-block .el-table {
  flex: 1;
  min-height: 0;

  .el-table__body-wrapper {
    overflow-y: auto;
  }
}

.video-title-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.pagination-container {
  margin: 16px 0 0 0;
  display: flex;
  justify-content: center;
}

/* 公共样式 */
.area-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.view-all-link {
  font-size: 13px;
  margin-left: 8px;
}

.video-title-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 公共卡片样式 */
.video-preview-block,
.vehicle-card-list-block,
.video-list-block,
.distribution-history-block {
  background: #1a3356;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  min-height: 0;
}

/* 响应式处理 */
@media (max-width: 1600px) {
  .grid-container {
    grid-template-columns: minmax(0, 2fr) minmax(300px, 350px);
  }

  .video-info-block {
    flex: 0 0 240px;
  }
}

@media (max-width: 1400px) {
  .grid-container {
    grid-template-columns: 1fr 320px;
    gap: 12px;
    padding: 12px;
  }

  .video-content-wrapper {
    flex-direction: column;
    gap: 12px;
  }

  .video-info-block {
    flex: 0 0 auto;
    max-height: 180px;
  }

  .video-player {
    min-height: 160px;
    max-height: 200px;
  }
}

@media (max-width: 1200px) {
  .grid-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto;
    gap: 12px;
    min-height: auto;
    max-height: none;
  }

  .grid-item-1-1 {
    grid-column: 1;
    grid-row: 1;
    min-height: 300px;
  }

  .grid-item-1-2 {
    grid-column: 1;
    grid-row: 2;
    min-height: 250px;
  }

  .grid-item-2-1 {
    grid-column: 1;
    grid-row: 3;
    min-height: 400px;
  }

  .grid-item-2-2 {
    grid-column: 1;
    grid-row: 4;
    min-height: 300px;
  }

  .video-content-wrapper {
    flex-direction: column;
  }

  .video-info-block {
    max-height: 120px;
  }

  .video-player {
    min-height: 140px;
    max-height: 160px;
  }
}

@media (max-width: 768px) {
  .grid-container {
    padding: 8px;
    gap: 8px;
  }

  .video-preview-block,
  .vehicle-card-list-block,
  .video-list-block,
  .distribution-history-block {
    padding: 12px;
  }

  .area-title {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .vehicle-card {
    padding: 8px;
    gap: 8px;
  }

  .vehicle-icon-box {
    width: 32px;
    height: 32px;
  }

  .vehicle-icon {
    font-size: 18px;
  }

  .vehicle-code-row {
    font-size: 14px;
  }

  .video-info-block {
    padding: 12px;
    gap: 8px;
  }

  .video-title {
    font-size: 16px;
  }

  .list-operations {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .video-list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .video-list-header .area-title {
    text-align: center;
  }

  .search-input {
    width: 100%;
  }
}
</style>
