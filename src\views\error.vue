<script lang="ts">
import { defineComponent, computed } from "vue";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "Error",
  props: {
    code: {
      type: String,
      default: "404"
    },
    desc: String
  },
  setup(props) {
    const router = useRouter();
    
    const getErrorDesc = computed(() => {
      switch (props.code) {
        case "404":
          return props.desc || "哎呀！页面跑丢了 (●'◡'●)";
        case "403":
          return props.desc || "抱歉啦，你没有权限访问这里 ╮(╯▽╰)╭";
        case "500":
          return props.desc || "服务器累趴了，需要休息一下 (≧﹏≦)";
        default:
          return props.desc || "出错啦！让我们返回首页吧 (｡ŏ_ŏ)";
      }
    });

    const getEmoji = computed(() => {
      switch (props.code) {
        case "404":
          return "🔍";
        case "403":
          return "🔒";
        case "500":
          return "🛠️";
        default:
          return "⚠️";
      }
    });

    return { 
      errorCode: computed(() => props.code),
      errorDesc: getErrorDesc,
      emoji: getEmoji,
      router
    };
  }
});
</script>

<template>
  <div class="error-page">
    <div class="error-container">
      <div class="error-emoji" :class="{ 'bounce': true }">{{ emoji }}</div>
      <div class="error-code">{{ errorCode }}</div>
      <div class="error-desc">{{ errorDesc }}</div>
      <el-button type="primary" class="home-button" @click="router.push('/')">
        <span class="button-icon">🏠</span>
        <span>返回首页</span>
      </el-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
.error-page {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--el-bg-color);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, 
      var(--el-color-primary-light-9) 0%,
      transparent 70%);
    opacity: 0.5;
    animation: rotate 60s linear infinite;
  }
}

.error-container {
  position: relative;
  text-align: center;
  padding: 40px;
  background: var(--el-bg-color);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  
  .error-emoji {
    font-size: 80px;
    margin-bottom: 20px;
    display: inline-block;
    
    &.bounce {
      animation: bounce 2s ease infinite;
    }
  }
  
  .error-code {
    font-size: 120px;
    font-weight: bold;
    background: linear-gradient(135deg, 
      var(--el-color-primary) 0%,
      var(--el-color-primary-light-3) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 20px;
    line-height: 1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .error-desc {
    font-size: 24px;
    color: var(--el-text-color-regular);
    margin-bottom: 30px;
    animation: fadeIn 0.5s ease;
  }
  
  .home-button {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 24px;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(var(--el-color-primary-rgb), 0.4);
    }
    
    .button-icon {
      font-size: 20px;
      margin-right: 8px;
      display: inline-block;
      transition: transform 0.3s ease;
    }
    
    &:hover .button-icon {
      transform: scale(1.2);
    }
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 深色主题适配
:deep(.dark) {
  .error-page {
    &::before {
      background: radial-gradient(circle at center, 
        rgba(var(--el-color-primary-rgb), 0.2) 0%,
        transparent 70%);
    }
  }
  
  .error-container {
    background: var(--el-bg-color-overlay);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }
}
</style>
