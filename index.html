<!DOCTYPE html>
<html lang="en" class="deep-blue-theme">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="stylesheet" href="/reset.css">
    <link rel="stylesheet" href="/element-ui-fix.css">
    <link rel="stylesheet" href="/sidebar-fix.css">
    <link rel="stylesheet" href="/menu-color-fix.css">
    <link rel="stylesheet" href="/login-fix.css">
    <link rel="stylesheet" href="/force-icon-fix.css">
    <title>智能巡逻 - 指挥系统</title>
    <script>
      //全局钩子
      window.SITE_CONFIG = {
        //api
        apiURL: "<%=apiURL%>"
      };
    </script>
    <script src="https://cdn.jsdelivr.net/npm/@stomp/stompjs@7.0.0/bundles/stomp.umd.min.js"></script>
    <style>
      /* 强制覆盖侧边栏样式 - 解决红色框问题 */
      .deep-blue-theme .el-aside.rr-sidebar,
      .deep-blue-theme .sidebar-container,
      .deep-blue-theme .rr-sidebar,
      .deep-blue-theme .rr-body .rr-sidebar,
      .deep-blue-theme .rr-sidebar-menu,
      .deep-blue-theme .sidebar-wrapper {
        background-color: #0A1F3D !important;
      }
      
      /* 修复移动侧边栏 */
      .deep-blue-theme .el-drawer,
      .deep-blue-theme .el-drawer__body,
      .deep-blue-theme .rr-sidebar-mobile,
      .deep-blue-theme .rr-sidebar-mobile-inner {
        background-color: #0A1F3D !important;
      }
      
      /* 修复菜单项 */
      .deep-blue-theme .el-menu,
      .deep-blue-theme .el-menu-item,
      .deep-blue-theme .el-submenu__title {
        background-color: #0A1F3D !important;
      }
      
      /* 直接针对DOM结构进行样式覆盖 */
      .deep-blue-theme ul[role="menubar"].el-menu {
        background-color: #0A1F3D !important;
      }
      
      .deep-blue-theme li.el-sub-menu > div.el-sub-menu__title {
        background-color: #0A1F3D !important;
        color: white !important;
      }
      
      /* 加强子菜单样式覆盖 */
      .deep-blue-theme ul[role="menu"].el-menu--inline,
      .deep-blue-theme .el-menu--inline,
      .deep-blue-theme .el-sub-menu .el-menu--inline,
      .deep-blue-theme li.el-sub-menu ul.el-menu--inline {
        background-color: #071A33 !important;
      }
      
      .deep-blue-theme ul[role="menu"].el-menu--inline > li.el-menu-item,
      .deep-blue-theme .el-menu--inline li.el-menu-item,
      .deep-blue-theme .el-menu--inline .el-menu-item {
        background-color: #071A33 !important;
        color: white !important;
      }
      
      /* 覆盖内联样式 */
      .deep-blue-theme .el-menu--inline li[style],
      .deep-blue-theme .el-menu-item[style] {
        background-color: #071A33 !important;
      }
      
      /* 确保SVG图标为白色 */
      .deep-blue-theme .el-menu svg path {
        fill: white !important;
      }
    </style>
  </head>
  <body class="deep-blue-theme">
    <div id="app"></div>
    <script type="module" src="./src/main.ts"></script>
  </body>
</html>
