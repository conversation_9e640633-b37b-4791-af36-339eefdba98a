{"name": "renren-ui", "version": "5.4.0", "private": true, "scripts": {"dev": "vite", "build": "npm run build:prod", "build:prod": "vue-tsc --noEmit && vite build --mode production", "serve": "npm run build && vite preview", "lint": "eslint \"src/**/*.{vue,ts}\" --fix"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"src/**/*.{ts,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "2.3.1", "@stomp/stompjs": "^7.1.1", "@vueuse/core": "9.1.1", "@wangeditor/editor": "5.1.1", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "1.6.0", "classnames": "^2.3.1", "core-js": "^3.14.0", "echarts": "^5.2.2", "element-plus": "2.4.4", "flv.js": "^1.6.2", "lodash": "^4.17.21", "mitt": "^2.1.0", "mpegts.js": "^1.8.0", "nprogress": "^0.2.0", "particlesjs": "^2.2.3", "pinia": "2.1.7", "qs": "^6.10.1", "quill": "^1.3.7", "sockjs-client": "^1.6.1", "srs-player": "^0.1.3", "vue": "^3.4.3", "vue-echarts": "^6.0.0", "vue-router": "4.2.5"}, "devDependencies": {"@types/lodash": "^4.14.172", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.6", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "@vitejs/plugin-vue": "^4.4.0", "@vue/compiler-sfc": "^3.4.3", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "eslint": "^8.13.0", "eslint-plugin-vue": "^8.6.0", "less": "^4.1.1", "less-loader": "^10.0.0", "lint-staged": "^11.0.0", "prettier": "^2.6.2", "sass": "^1.50.1", "typescript": "^4.6.3", "vite": "4.4.12", "vite-plugin-html": "^2.1.1", "vite-plugin-svg-icons": "2.0.1", "vite-tsconfig-paths": "3.4.0", "vue-tsc": "1.8.27"}}