<template>
  <el-dialog
    :title="!dataForm.id ? '上传视频' : '修改'"
    v-model="visible"
    :close-on-click-modal="false"
    width="650px"
  >
    <el-form
      :model="dataForm"
      :rules="dataRule"
      ref="dataFormRef"
      label-width="100px"
      @keyup.enter="dataFormSubmitHandle()"
    >
      <el-form-item label="视频标题" prop="title">
        <el-input v-model="dataForm.title" placeholder="请输入视频标题"></el-input>
      </el-form-item>
      <el-form-item label="视频描述" prop="description">
        <el-input
          v-model="dataForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入视频描述"
        ></el-input>
      </el-form-item>
      <el-form-item label="视频文件" prop="filePath">
        <el-upload
          class="upload-demo"
          action="#"
          :http-request="uploadVideo"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept="video/*"
          :disabled="uploadLoading"
        >
          <template #trigger>
            <el-button type="primary" :disabled="uploadLoading">选择视频</el-button>
          </template>
          <template #tip>
            <div class="el-upload__tip">支持mp4、avi等格式视频文件，大小不超过50MB</div>
          </template>
        </el-upload>

        <!-- 视频信息显示 -->
        <div
          v-if="dataForm.duration && dataForm.duration !== '00:00'"
          class="video-info-container"
        >
          <div class="video-info-header">
            <span class="video-info-title">
              <el-icon class="video-info-icon"><VideoPlay /></el-icon>
              视频信息
            </span>
            <el-tag type="success" size="small" effect="dark" class="status-tag">
              已选择
            </el-tag>
          </div>
          <div class="video-info-content">
            <div class="info-item">
              <div class="info-icon-wrapper">
                <el-icon class="info-icon"><Clock /></el-icon>
              </div>
              <div class="info-text">
                <span class="info-label">时长</span>
                <span class="info-value">{{ dataForm.duration }}</span>
              </div>
            </div>
            <div class="info-item">
              <div class="info-icon-wrapper">
                <el-icon class="info-icon"><Document /></el-icon>
              </div>
              <div class="info-text">
                <span class="info-label">大小</span>
                <span class="info-value">{{ dataForm.fileSize }} MB</span>
              </div>
            </div>
            <div class="info-item" v-if="videoFile">
              <div class="info-icon-wrapper">
                <el-icon class="info-icon"><Folder /></el-icon>
              </div>
              <div class="info-text">
                <span class="info-label">文件名</span>
                <span class="info-value">{{ videoFile.name }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 上传进度条 -->
        <div v-if="uploadProgress > 0" class="upload-progress-container">
          <div class="upload-progress-header">
            <span class="upload-progress-title">
              <i class="el-icon-upload"></i> 上传进度
            </span>
            <span class="upload-progress-percentage">{{ uploadProgress }}%</span>
          </div>
          <el-progress
            :percentage="uploadProgress"
            :status="getProgressStatus()"
            :stroke-width="12"
            :format="() => ''"
            class="upload-progress-bar"
          ></el-progress>
          <div class="upload-progress-footer">
            <span class="upload-progress-status">{{ uploadProgressText }}</span>
            <span class="upload-progress-time" v-if="uploadStatus === 'uploading'">
              预计剩余时间: {{ estimatedTimeRemaining }}
            </span>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false" :disabled="uploadLoading">取消</el-button>
        <el-button
          type="primary"
          @click="dataFormSubmitHandle()"
          :loading="uploadLoading"
        >
          {{ uploadLoading ? "上传中..." : "确定" }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
export default {
  name: "publicityvideo-add-or-update",
};
</script>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import type { FormInstance, UploadFile, UploadUserFile } from "element-plus";
import {
  Upload,
  Check,
  Close,
  Clock,
  VideoPlay,
  Document,
  Folder,
} from "@element-plus/icons-vue";
import http from "@/utils/http";
import app from "@/constants/app";
import { getToken } from "@/utils/cache";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref<FormInstance>();
const fileList = ref<UploadUserFile[]>([]);
const videoFile = ref<File | null>(null);
const uploadLoading = ref(false);
const uploadProgress = ref(0);
const uploadStatus = ref<"uploading" | "success" | "error" | "">("");
const uploadStartTime = ref<number>(0);

const estimatedTimeRemaining = computed(() => {
  if (
    uploadProgress.value <= 0 ||
    uploadProgress.value >= 100 ||
    uploadStatus.value !== "uploading" ||
    !uploadStartTime.value
  ) {
    return "计算中...";
  }

  const elapsedTime = (Date.now() - uploadStartTime.value) / 1000; // 已用时间（秒）
  const estimatedTotalTime = (elapsedTime / uploadProgress.value) * 100; // 预计总时间（秒）
  const remainingTime = estimatedTotalTime - elapsedTime; // 剩余时间（秒）

  if (remainingTime < 60) {
    return `${Math.round(remainingTime)}秒`;
  } else if (remainingTime < 3600) {
    return `${Math.floor(remainingTime / 60)}分${Math.round(remainingTime % 60)}秒`;
  } else {
    return `${Math.floor(remainingTime / 3600)}小时${Math.floor(
      (remainingTime % 3600) / 60
    )}分`;
  }
});

const uploadProgressText = computed(() => {
  if (uploadStatus.value === "uploading") {
    return `正在上传...`;
  } else if (uploadStatus.value === "success") {
    return "上传成功！";
  } else if (uploadStatus.value === "error") {
    return "上传失败，请重试";
  }
  return "";
});

const getProgressStatus = () => {
  if (uploadStatus.value === "success") return "success";
  if (uploadStatus.value === "error") return "exception";
  return "";
};

const dataForm = reactive({
  id: 0,
  title: "",
  description: "",
  duration: "",
  fileSize: 0,
  filePath: "",
  status: "可用",
  uploadTime: "",
  createdAt: "",
  updatedAt: "",
});

const formatFileSize = (size: number): number => {
  return parseFloat((size / (1024 * 1024)).toFixed(2));
};

// 获取视频详细信息
const getVideoMetadata = (
  file: File
): Promise<{
  duration: string;
  durationSeconds: number;
  width: number;
  height: number;
  aspectRatio: string;
}> => {
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    video.preload = "metadata";

    video.onloadedmetadata = () => {
      try {
        const durationSeconds = Math.floor(video.duration);
        const hours = Math.floor(durationSeconds / 3600);
        const minutes = Math.floor((durationSeconds % 3600) / 60);
        const seconds = durationSeconds % 60;

        // 格式化时长显示
        let durationString = "";
        if (hours > 0) {
          durationString = `${hours
            .toString()
            .padStart(2, "0")}:${minutes
            .toString()
            .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        } else {
          durationString = `${minutes
            .toString()
            .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
        }

        // 计算宽高比
        const aspectRatio = `${video.videoWidth}x${video.videoHeight}`;

        URL.revokeObjectURL(video.src);

        resolve({
          duration: durationString,
          durationSeconds: durationSeconds,
          width: video.videoWidth,
          height: video.videoHeight,
          aspectRatio: aspectRatio,
        });
      } catch (error) {
        URL.revokeObjectURL(video.src);
        reject(error);
      }
    };

    video.onerror = () => {
      URL.revokeObjectURL(video.src);
      reject(new Error("无法读取视频文件信息"));
    };

    video.src = URL.createObjectURL(file);
  });
};

const handleFileChange = async (file: UploadFile) => {
  if (file.raw) {
    try {
      videoFile.value = file.raw;
      dataForm.fileSize = formatFileSize(file.raw.size);

      // 获取视频元数据信息
      const metadata = await getVideoMetadata(file.raw);
      dataForm.duration = metadata.duration;
      dataForm.filePath = URL.createObjectURL(file.raw);

      // 显示视频信息
      ElMessage.success(
        `视频信息获取成功：时长 ${metadata.duration}，分辨率 ${metadata.aspectRatio}`
      );

      console.log("视频元数据:", {
        duration: metadata.duration,
        durationSeconds: metadata.durationSeconds,
        resolution: metadata.aspectRatio,
        fileSize: dataForm.fileSize + "MB",
      });
    } catch (error) {
      console.error("获取视频信息失败:", error);
      ElMessage.warning("无法获取视频信息，请确保选择的是有效的视频文件");
      // 即使获取元数据失败，也允许继续上传
      dataForm.duration = "00:00";
      dataForm.filePath = URL.createObjectURL(file.raw);
    }
  }
};

const handleFileRemove = () => {
  videoFile.value = null;
  dataForm.filePath = "";
  dataForm.fileSize = 0;
  dataForm.duration = "";
};

const uploadVideo = async () => {
  if (!videoFile.value) {
    ElMessage.error("请选择要上传的视频文件");
    return Promise.reject(new Error("没有选择视频文件"));
  }

  if (!dataForm.title || !dataForm.description) {
    ElMessage.error("请填写视频标题和描述");
    return Promise.reject(new Error("标题或描述为空"));
  }

  // 检查文件大小
  const maxSizeMB = 50; // 设置最大文件大小为50MB
  const fileSizeMB = videoFile.value.size / (1024 * 1024);

  if (fileSizeMB > maxSizeMB) {
    ElMessage.error(
      `文件大小超过限制（${maxSizeMB}MB），当前文件大小：${fileSizeMB.toFixed(2)}MB`
    );
    return Promise.reject(new Error("文件大小超过限制"));
  }

  uploadLoading.value = true;
  uploadProgress.value = 0;
  uploadStatus.value = "uploading";
  uploadStartTime.value = Date.now();

  return new Promise((resolve, reject) => {
    try {
      // 创建FormData对象
      const formData = new FormData();
      formData.append("title", dataForm.title);
      formData.append("description", dataForm.description);
      formData.append("duration", dataForm.duration); // 添加视频时长
      formData.append("fileSize", dataForm.fileSize.toString()); // 添加文件大小
      // 确保videoFile.value不为null
      if (videoFile.value) {
        formData.append("file", videoFile.value);
      }

      // 使用XMLHttpRequest直接发送FormData，完全控制请求头和进度
      const xhr = new XMLHttpRequest();
      // 修正API URL路径，确保以/开头
      const apiUrl = `${app.api}task/publicityvideo/vedio/upload`;
      xhr.open("POST", apiUrl, true);

      // 添加token和其他需要的头信息
      const token = getToken();
      if (token) {
        xhr.setRequestHeader("token", token);
      }
      xhr.setRequestHeader("X-Requested-With", "XMLHttpRequest");
      // 不要设置Content-Type，让浏览器自动处理

      // 监听上传进度
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          uploadProgress.value = Math.floor((event.loaded * 100) / (event.total || 1));
          console.log(`上传进度: ${uploadProgress.value}%`);
        }
      };

      // 处理完成事件
      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            if (response.code === 0) {
              uploadStatus.value = "success";
              uploadProgress.value = 100;

              // 如果后端返回了文件路径，可以更新本地的filePath
              if (response.data && response.data.filePath) {
                dataForm.filePath = response.data.filePath;
              }

              ElMessage.success("视频上传成功");
              resolve(response.data);
            } else {
              uploadStatus.value = "error";
              ElMessage.error(response.msg || "上传失败");
              reject(new Error(response.msg || "上传失败"));
            }
          } catch (e) {
            uploadStatus.value = "error";
            ElMessage.error("视频上传失败: 响应解析错误");
            reject(new Error("响应解析错误"));
          }
        } else if (xhr.status === 413) {
          uploadStatus.value = "error";
          ElMessage({
            type: "error",
            duration: 10000, // 显示更长时间
            showClose: true,
            message:
              "上传的文件太大，超过了后端允许的最大限制。请联系管理员调整Spring Boot配置中的 'spring.servlet.multipart.max-file-size' 和 'spring.servlet.multipart.max-request-size' 参数。",
          });
          reject(new Error("文件太大，超过服务器限制"));
        } else {
          uploadStatus.value = "error";
          ElMessage.error(`上传失败: 服务器返回 ${xhr.status}`);
          reject(new Error(`服务器返回 ${xhr.status}`));
        }
        uploadLoading.value = false;
      };

      // 处理错误
      xhr.onerror = () => {
        uploadStatus.value = "error";
        uploadLoading.value = false;
        ElMessage.error("视频上传失败: 网络错误");
        reject(new Error("网络错误"));
      };

      // 发送请求
      xhr.send(formData);

      console.log("上传请求已发送到:", apiUrl);
      console.log("上传的表单数据:", {
        title: dataForm.title,
        description: dataForm.description,
        duration: dataForm.duration,
        fileSize: dataForm.fileSize,
        fileName: videoFile.value?.name,
        fileType: videoFile.value?.type,
      });
    } catch (error: any) {
      uploadStatus.value = "error";
      uploadLoading.value = false;
      ElMessage.error(error.message || "视频上传失败");
      reject(error);
    }
  });
};

const dataRule = reactive({
  title: [{ required: true, message: "视频标题不能为空", trigger: "blur" }],
  description: [{ required: true, message: "视频描述不能为空", trigger: "blur" }],
  status: [{ required: true, message: "请选择视频状态", trigger: "change" }],
});

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value?.validate(async (valid) => {
    if (!valid) {
      return false;
    }

    // 如果是新增且没有选择文件
    if (!dataForm.id && !videoFile.value) {
      ElMessage.error("请选择要上传的视频文件");
      return;
    }

    try {
      // 上传视频文件（如果有选择文件）
      if (videoFile.value) {
        await uploadVideo();
      }

      // 关闭弹窗，刷新列表页
      visible.value = false;
      emit("refreshDataList");
    } catch (e) {
      console.error("保存失败", e);
    }
  });
};

// 初始化
const init = (id?: number) => {
  dataForm.id = id || 0;
  visible.value = true;
  fileList.value = [];
  videoFile.value = null;
  uploadProgress.value = 0;
  uploadStatus.value = "";
  uploadStartTime.value = 0;

  // 重置表单数据
  dataForm.title = "";
  dataForm.description = "";
  dataForm.duration = "";
  dataForm.fileSize = 0;
  dataForm.filePath = "";
  dataForm.status = "可用";
  dataForm.uploadTime = "";
  dataForm.createdAt = "";
  dataForm.updatedAt = "";

  // 如果是修改，则查询详情
  if (dataForm.id) {
    // 模拟获取数据
    setTimeout(() => {
      const mockData = {
        id: dataForm.id,
        title: `宣传视频 ${dataForm.id}`,
        description: `这是第 ${dataForm.id} 个宣传视频的描述内容，包含了相关的宣传信息和描述说明`,
        duration: "03:25",
        fileSize: 25.6,
        filePath: "",
        status: "可用",
        uploadTime: "2025/07/01 14:13",
        createdAt: "2025/07/01 14:13",
        updatedAt: "2025/07/01 14:13",
      };

      // 赋值
      Object.assign(dataForm, mockData);

      // 如果有文件路径，模拟已上传文件
      if (dataForm.filePath) {
        fileList.value = [
          {
            name: `视频${dataForm.id}.mp4`,
            url: dataForm.filePath,
          },
        ];
      }
    }, 100);
  }
};

// 对外暴露的方法
defineExpose({
  init,
});
</script>

<style lang="less" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.video-info-container {
  margin-top: 15px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  padding: 18px;
  border: 1px solid rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
  }

  .video-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .video-info-title {
      font-weight: 600;
      color: #60a5fa;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;

      .video-info-icon {
        font-size: 18px;
        color: #60a5fa;
      }
    }

    .status-tag {
      background: rgba(34, 197, 94, 0.2) !important;
      border-color: rgba(34, 197, 94, 0.3) !important;
      color: #4ade80 !important;
    }
  }

  .video-info-content {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .info-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 0;

      .info-icon-wrapper {
        width: 32px;
        height: 32px;
        background: rgba(96, 165, 250, 0.15);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        .info-icon {
          font-size: 16px;
          color: #60a5fa;
        }
      }

      .info-text {
        display: flex;
        flex-direction: column;
        gap: 2px;
        flex: 1;

        .info-label {
          color: rgba(255, 255, 255, 0.6);
          font-size: 12px;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        .info-value {
          color: rgba(255, 255, 255, 0.95);
          font-weight: 600;
          font-size: 14px;
          word-break: break-all;
        }
      }
    }
  }
}

.upload-progress-container {
  margin-top: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid #e6e9ed;

  .upload-progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    .upload-progress-title {
      font-weight: 500;
      color: #606266;
      display: flex;
      align-items: center;

      i {
        margin-right: 5px;
        font-size: 16px;
        color: #409eff;
      }
    }

    .upload-progress-percentage {
      font-weight: bold;
      color: #409eff;
      font-size: 16px;
    }
  }

  .upload-progress-bar {
    margin-bottom: 10px;
  }

  .upload-progress-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;

    .upload-progress-status {
      color: #606266;
      font-weight: 500;
    }

    .upload-progress-time {
      color: #909399;
    }
  }
}

.el-upload__tip {
  line-height: 1.5;
  margin-top: 8px;
  color: #909399;
}
</style>
