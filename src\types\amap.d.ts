// 高德地图API类型声明
declare namespace AMap {
  // 基础类型
  class LngLat {
    constructor(lng: number, lat: number);
    getLng(): number;
    getLat(): number;
  }
  
  class Pixel {
    constructor(x: number, y: number);
    getX(): number;
    getY(): number;
  }
  
  interface MapOptions {
    zoom?: number;
    center?: [number, number] | LngLat;
    viewMode?: string;
    mapStyle?: string;
  }
  
  // 地图相关
  class Map {
    constructor(container: string | HTMLElement, options?: MapOptions);
    setCenter(position: LngLat | [number, number]): void;
    setZoom(zoom: number): void;
    add(overlay: any | any[]): void;
    remove(overlay: any | any[]): void;
  }
  
  // 标记相关
  interface MarkerOptions {
    position?: LngLat | [number, number];
    title?: string;
    icon?: string;
    content?: HTMLElement | string;
    offset?: Pixel;
    anchor?: string;
    angle?: number;
    autoRotation?: boolean;
    extData?: any;
  }
  
  class Marker {
    constructor(options: MarkerOptions);
    setPosition(position: LngLat | [number, number]): void;
    getPosition(): LngLat;
    setAngle(angle: number): void;
    getAngle(): number;
    setExtData(extData: any): void;
    getExtData(): any;
    setContent(content: HTMLElement | string): void;
    getContent(): HTMLElement | string;
    on(event: string, callback: (...args: any[]) => void): void;
    moveTo(position: LngLat | [number, number], opts?: {duration?: number, autoRotation?: boolean}): void;
  }
  
  // 文本标记
  interface TextOptions {
    text: string;
    position: LngLat | [number, number];
    offset?: Pixel;
    anchor?: string;
    style?: Record<string, any>;
  }
  
  class Text {
    constructor(options: TextOptions);
    setText(text: string): void;
    getText(): string;
    setPosition(position: LngLat | [number, number]): void;
    getPosition(): LngLat;
  }
  
  // 信息窗体
  interface InfoWindowOptions {
    content?: HTMLElement | string;
    position?: LngLat | [number, number];
    offset?: Pixel;
    anchor?: string;
  }
  
  class InfoWindow {
    constructor(options?: InfoWindowOptions);
    open(map: Map, position?: LngLat | [number, number]): void;
    close(): void;
    setContent(content: HTMLElement | string): void;
    getContent(): HTMLElement | string;
  }
  
  // 命名空间声明
  namespace TileLayer {
    class Traffic {
      constructor(options?: Record<string, any>);
    }
  }
  
  // 工具类
  class Scale {
    constructor(options?: Record<string, any>);
  }
  
  class ToolBar {
    constructor(options?: Record<string, any>);
  }
  
  class MouseTool {
    constructor(map: Map);
  }
  
  namespace MoveAnimation {
    // 移动动画相关API
  }
}

// 扩展Window接口
interface Window {
  AMap: typeof AMap;
  _AMapSecurityConfig: {
    securityJsCode: string;
  };
} 