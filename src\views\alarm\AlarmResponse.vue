<template>
  <div class="alarm-response">
    <div class="module-header">
      <div class="title-section">
        <i class="el-icon-warning-outline"></i>
        <h2>一键告警响应中心</h2>
      </div>
      <el-tag type="success" effect="dark">监控中</el-tag>
    </div>

    <el-row :gutter="20">
      <el-col :span="16">
        <div class="video-card">
          <div class="card-header">
            <div class="header-title">
              <i class="el-icon-video-camera"></i>
              <span>实时视频监控</span>
            </div>
            <div class="status-indicator">
              <span class="status-dot" :class="{ active: isVideoActive }"></span>
              <span>{{ isVideoActive ? "已连接" : "未连接" }}</span>
            </div>
          </div>
          <div class="video-container">
            <video
              ref="videoRef"
              autoplay
              playsinline
              muted
              style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px"
            />
            <div v-if="!isVideoActive" class="video-placeholder">
              <i class="el-icon-video-camera"></i>
              <p>摄像头未连接</p>
              <el-button size="small" type="primary" @click="connectCamera"
                >连接摄像头</el-button
              >
            </div>
            <div class="video-overlay" v-if="showAlarm">
              <div class="alarm-indicator">
                <i class="el-icon-warning"></i>
                <span>紧急告警中</span>
              </div>
            </div>
            <div class="video-info">
              <div class="camera-name">前置摄像头</div>
              <div class="timestamp">{{ currentTime }}</div>
            </div>
          </div>
          <div class="video-controls">
            <el-button-group>
              <el-button size="small" :disabled="!isVideoActive">
                <i class="el-icon-camera-solid"></i> 截图
              </el-button>
              <el-button size="small" :disabled="!isVideoActive">
                <i class="el-icon-video-camera"></i> 录像
              </el-button>
              <el-button size="small" type="warning" @click="toggleFullscreen">
                <i class="el-icon-full-screen"></i> 全屏
              </el-button>
            </el-button-group>
            <div class="camera-selector">
              <el-select v-model="currentCamera" size="small" style="width: 150px">
                <el-option label="前置摄像头" value="front" />
                <el-option label="后置摄像头" value="rear" />
                <el-option label="侧面摄像头" value="side" />
              </el-select>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="communication-card">
          <div class="card-header">
            <div class="header-title">
              <i class="el-icon-phone"></i>
              <span>通讯对讲</span>
            </div>
            <el-tag
              size="small"
              :type="communicationStatus === 'connected' ? 'success' : 'info'"
            >
              {{ communicationStatusText }}
            </el-tag>
          </div>
          <div class="intercom-section">
            <div class="call-status">
              <div
                class="status-icon"
                :class="{ active: communicationStatus === 'connected' }"
              >
                <i
                  :class="
                    communicationStatus === 'connected'
                      ? 'el-icon-microphone'
                      : 'el-icon-microphone'
                  "
                ></i>
              </div>
              <div class="status-text">{{ communicationStatusText }}</div>
              <div class="call-duration" v-if="communicationStatus === 'connected'">
                {{ callDuration }}
              </div>
            </div>
            <div class="call-controls">
              <el-button-group>
                <el-button
                  type="success"
                  circle
                  @click="toggleCommunication"
                  :class="{ active: communicationStatus === 'connected' }"
                >
                  <i
                    :class="
                      communicationStatus === 'connected'
                        ? 'el-icon-phone'
                        : 'el-icon-phone-outline'
                    "
                  ></i>
                </el-button>
                <el-button
                  type="primary"
                  circle
                  :disabled="communicationStatus !== 'connected'"
                >
                  <i class="el-icon-microphone"></i>
                </el-button>
                <el-button
                  type="info"
                  circle
                  :disabled="communicationStatus !== 'connected'"
                >
                  <i class="el-icon-headset"></i>
                </el-button>
                <el-button
                  type="danger"
                  circle
                  @click="endCommunication"
                  :disabled="communicationStatus !== 'connected'"
                >
                  <i class="el-icon-close"></i>
                </el-button>
              </el-button-group>
            </div>
          </div>
          <div class="alarm-journal">
            <div class="card-subheader">
              <div class="subheader-title">告警日志</div>
              <el-button size="small" type="text">查看更多</el-button>
            </div>
            <div class="journal-list" ref="journalListRef">
              <div
                v-for="(log, idx) in alarmLogs"
                :key="idx"
                class="journal-item"
                :class="{ 'is-alarm': log.type === 'alarm' }"
              >
                <div class="log-icon">
                  <i :class="getLogIcon(log.type)"></i>
                </div>
                <div class="log-content">
                  <div class="log-message">{{ log.message }}</div>
                  <div class="log-time">{{ log.time }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="quick-actions">
            <div class="card-subheader">
              <div class="subheader-title">快捷操作</div>
            </div>
            <div class="action-buttons">
              <el-button type="danger" @click="simulateAlarm">模拟告警</el-button>
              <el-button type="warning" @click="sendEmergencyMessage"
                >发送紧急消息</el-button
              >
              <el-button type="info" @click="sendVoice">语音对讲</el-button>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <el-dialog v-model="emergencyMessageDialog" title="发送紧急消息" width="30%">
      <el-form>
        <el-form-item label="消息类型">
          <el-select v-model="emergencyMessage.type" style="width: 100%">
            <el-option label="紧急通知" value="emergency" />
            <el-option label="安全警告" value="warning" />
            <el-option label="交通提醒" value="traffic" />
          </el-select>
        </el-form-item>
        <el-form-item label="消息内容">
          <el-input v-model="emergencyMessage.content" type="textarea" rows="4" />
        </el-form-item>
        <el-form-item label="是否语音播报">
          <el-switch v-model="emergencyMessage.useVoice" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="emergencyMessageDialog = false">取消</el-button>
          <el-button type="primary" @click="submitEmergencyMessage">发送</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="voiceDialog" title="语音对讲" width="30%">
      <div class="voice-dialog-content">
        <el-input
          v-model="voiceInput"
          placeholder="输入对讲内容..."
          type="textarea"
          rows="4"
          @keyup.enter="submitVoice"
        />
        <div class="dialog-voice-history">
          <div v-for="(msg, idx) in voiceHistory" :key="idx" class="voice-history-item">
            {{ msg }}
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="voiceDialog = false">关闭</el-button>
          <el-button type="primary" @click="submitVoice">发送语音</el-button>
          <el-button type="success" @click="startListening" :loading="listeningForVoice">
            <i class="el-icon-microphone"></i> 语音输入
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";

// 视频相关状态
const videoRef = ref<HTMLVideoElement | null>(null);
const isVideoActive = ref(false);
const currentCamera = ref("front");
const currentTime = ref("");
const timerId = ref<number | null>(null);

// 告警相关状态
const showAlarm = ref(false);
const alarmTimeout = ref<number | null>(null);

// 通讯相关状态
const communicationStatus = ref<"idle" | "connecting" | "connected" | "disconnected">(
  "idle"
);
const callStartTime = ref<Date | null>(null);
const callDuration = ref("00:00");
const callTimerId = ref<number | null>(null);

// 弹窗相关状态
const emergencyMessageDialog = ref(false);
const voiceDialog = ref(false);
const emergencyMessage = ref({
  type: "emergency",
  content: "",
  useVoice: true,
});

// 语音对讲相关状态
const voiceInput = ref("");
const voiceHistory = ref<string[]>([]);
const listeningForVoice = ref(false);

// 告警日志
const journalListRef = ref<HTMLElement | null>(null);
const alarmLogs = ref<Array<{ type: string; message: string; time: string }>>([
  { type: "info", message: "系统启动", time: "09:30:00" },
  { type: "info", message: "连接到远程服务器", time: "09:30:05" },
  { type: "warning", message: "低电量警告", time: "10:15:22" },
  { type: "info", message: "开始巡逻任务", time: "10:30:00" },
  { type: "alarm", message: "收到一键告警请求", time: "11:45:30" },
  { type: "info", message: "已响应告警", time: "11:46:05" },
]);

// 计算属性
const communicationStatusText = computed(() => {
  switch (communicationStatus.value) {
    case "idle":
      return "待机中";
    case "connecting":
      return "连接中...";
    case "connected":
      return "通话中";
    case "disconnected":
      return "已断开";
    default:
      return "";
  }
});

// 获取摄像头视频流
function connectCamera() {
  if (navigator.mediaDevices?.getUserMedia) {
    navigator.mediaDevices
      .getUserMedia({ video: true, audio: false })
      .then((stream) => {
        if (videoRef.value) {
          videoRef.value.srcObject = stream;
          isVideoActive.value = true;
          addLog("info", "摄像头已连接");
        }
      })
      .catch(() => {
        ElMessage.error("无法获取摄像头视频流");
        addLog("warning", "摄像头连接失败");
      });
  }
}

// 更新当前时间
function updateCurrentTime() {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");
  currentTime.value = `${hours}:${minutes}:${seconds}`;
}

// 切换通讯状态
function toggleCommunication() {
  if (
    communicationStatus.value === "idle" ||
    communicationStatus.value === "disconnected"
  ) {
    communicationStatus.value = "connecting";

    setTimeout(() => {
      communicationStatus.value = "connected";
      callStartTime.value = new Date();
      startCallTimer();
      addLog("info", "已建立语音通话");
    }, 1000);
  } else if (communicationStatus.value === "connected") {
    endCommunication();
  }
}

// 结束通讯
function endCommunication() {
  if (communicationStatus.value === "connected") {
    communicationStatus.value = "disconnected";
    stopCallTimer();
    callStartTime.value = null;
    addLog("info", "通话已结束");
  }
}

// 开始计时
function startCallTimer() {
  if (callTimerId.value) clearInterval(callTimerId.value);

  callTimerId.value = window.setInterval(() => {
    if (!callStartTime.value) return;

    const now = new Date();
    const diff = Math.floor((now.getTime() - callStartTime.value.getTime()) / 1000);
    const minutes = Math.floor(diff / 60)
      .toString()
      .padStart(2, "0");
    const seconds = (diff % 60).toString().padStart(2, "0");
    callDuration.value = `${minutes}:${seconds}`;
  }, 1000);
}

// 停止计时
function stopCallTimer() {
  if (callTimerId.value) {
    clearInterval(callTimerId.value);
    callTimerId.value = null;
  }
  callDuration.value = "00:00";
}

// 发送语音消息
function sendVoice() {
  voiceDialog.value = true;
}

// 提交语音对讲
function submitVoice() {
  if (!voiceInput.value.trim()) return;

  voiceHistory.value.push(`我: ${voiceInput.value}`);
  speak(voiceInput.value);

  setTimeout(() => {
    const response = "车端: 收到您的消息，已转达给现场人员。";
    voiceHistory.value.push(response);
    speak(response.replace("车端: ", ""));
  }, 1000);

  voiceInput.value = "";
}

// 语音识别
function startListening() {
  if (!("webkitSpeechRecognition" in window)) {
    ElMessage.warning("当前浏览器不支持语音识别");
    return;
  }

  const recognition = new (window as any).webkitSpeechRecognition();
  recognition.lang = "zh-CN";
  recognition.continuous = false;
  recognition.interimResults = false;

  listeningForVoice.value = true;

  recognition.onresult = (event: any) => {
    const transcript = event.results[0][0].transcript;
    voiceInput.value = transcript;
    listeningForVoice.value = false;
  };

  recognition.onerror = () => {
    listeningForVoice.value = false;
    ElMessage.error("语音识别失败");
  };

  recognition.onend = () => {
    listeningForVoice.value = false;
  };

  recognition.start();
}

// 语音播报
function speak(text: string) {
  if ("speechSynthesis" in window) {
    window.speechSynthesis.cancel(); // 取消之前的语音

    const utter = new window.SpeechSynthesisUtterance(text);
    utter.lang = "zh-CN";

    // 获取中文语音
    const voices = window.speechSynthesis.getVoices();
    const chineseVoice = voices.find((voice) => voice.lang.includes("zh"));
    if (chineseVoice) {
      utter.voice = chineseVoice;
    }

    window.speechSynthesis.speak(utter);
  }
}

// 模拟告警
function simulateAlarm() {
  ElMessageBox.confirm("确定要模拟发送一键告警吗？", "告警确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      showAlarm.value = true;
      ElMessage({
        type: "error",
        message: "收到一键告警请求！",
        duration: 0,
        showClose: true,
      });

      addLog("alarm", "收到一键告警请求");

      // 自动响应告警
      if (communicationStatus.value !== "connected") {
        toggleCommunication();
      }

      // 5秒后自动取消告警状态
      if (alarmTimeout.value) clearTimeout(alarmTimeout.value);
      alarmTimeout.value = window.setTimeout(() => {
        showAlarm.value = false;
      }, 10000);
    })
    .catch(() => {});
}

// 发送紧急消息
function sendEmergencyMessage() {
  emergencyMessageDialog.value = true;
}

// 提交紧急消息
function submitEmergencyMessage() {
  if (!emergencyMessage.value.content) {
    ElMessage.warning("请输入消息内容");
    return;
  }

  const messageType = (() => {
    switch (emergencyMessage.value.type) {
      case "emergency":
        return "紧急通知";
      case "warning":
        return "安全警告";
      case "traffic":
        return "交通提醒";
      default:
        return "通知";
    }
  })();

  addLog(
    "info",
    `发送${messageType}: ${emergencyMessage.value.content.substring(0, 20)}...`
  );

  ElMessage.success("紧急消息已发送");

  if (emergencyMessage.value.useVoice) {
    speak(emergencyMessage.value.content);
  }

  emergencyMessageDialog.value = false;
  emergencyMessage.value.content = "";
}

// 添加日志
function addLog(type: string, message: string) {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");

  alarmLogs.value.push({
    type,
    message,
    time: `${hours}:${minutes}:${seconds}`,
  });

  // 滚动到底部
  setTimeout(() => {
    if (journalListRef.value) {
      journalListRef.value.scrollTop = journalListRef.value.scrollHeight;
    }
  }, 100);
}

// 获取日志图标
function getLogIcon(type: string) {
  switch (type) {
    case "info":
      return "el-icon-info";
    case "warning":
      return "el-icon-warning";
    case "alarm":
      return "el-icon-alarm-clock";
    default:
      return "el-icon-info";
  }
}

// 切换全屏
function toggleFullscreen() {
  const video = videoRef.value;
  if (!video) return;

  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    video.requestFullscreen().catch((err) => {
      ElMessage.error(`全屏切换失败: ${err.message}`);
    });
  }
}

// 组件挂载
onMounted(() => {
  // 自动连接摄像头
  connectCamera();

  // 更新时间
  updateCurrentTime();
  timerId.value = window.setInterval(updateCurrentTime, 1000);

  // 预加载语音引擎
  if ("speechSynthesis" in window) {
    window.speechSynthesis.getVoices();
  }
});

// 组件卸载前清理
onUnmounted(() => {
  // 停止时间更新
  if (timerId.value) {
    clearInterval(timerId.value);
  }

  // 停止告警超时
  if (alarmTimeout.value) {
    clearTimeout(alarmTimeout.value);
  }

  // 停止通话计时
  stopCallTimer();

  // 关闭摄像头
  if (videoRef.value && videoRef.value.srcObject) {
    const stream = videoRef.value.srcObject as MediaStream;
    stream.getTracks().forEach((track) => track.stop());
  }
});
</script>

<style scoped>
.alarm-response {
  padding: 20px;
}

.module-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-section h2 {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.video-card,
.communication-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-header {
  padding: 12px 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  color: #303133;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #909399;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #909399;
  display: inline-block;
}

.status-dot.active {
  background-color: #67c23a;
}

.video-container {
  position: relative;
  height: 300px;
  background: #000;
  overflow: hidden;
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
  gap: 10px;
}

.video-placeholder i {
  font-size: 40px;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 0, 0, 0.15);
  display: flex;
  justify-content: center;
  align-items: center;
  animation: pulse 1.5s infinite;
}

.alarm-indicator {
  background: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.video-info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.video-controls {
  padding: 12px 15px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.intercom-section {
  padding: 15px;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.call-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.status-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  color: #909399;
}

.status-icon.active {
  background: #67c23a;
  color: white;
  animation: pulse 1.5s infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

.call-duration {
  font-size: 12px;
  color: #909399;
}

.call-controls {
  display: flex;
  justify-content: center;
}

.call-controls .el-button-group {
  display: flex;
  gap: 10px;
}

.card-subheader {
  padding: 12px 15px;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.subheader-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.alarm-journal {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.journal-list {
  flex: 1;
  padding: 0 15px 15px;
  overflow-y: auto;
  max-height: 180px;
}

.journal-item {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

.journal-item.is-alarm {
  background: #ffebeb;
}

.log-icon {
  color: #909399;
}

.log-content {
  flex: 1;
}

.log-message {
  font-size: 12px;
}

.log-time {
  font-size: 10px;
  color: #c0c4cc;
}

.quick-actions {
  padding-bottom: 15px;
}

.action-buttons {
  padding: 0 15px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.voice-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.dialog-voice-history {
  height: 150px;
  overflow-y: auto;
  background: #f7f7f7;
  border-radius: 4px;
  padding: 10px;
}

.voice-history-item {
  margin-bottom: 8px;
  font-size: 14px;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.el-button.active {
  background-color: #f56c6c;
  border-color: #f56c6c;
  color: white;
}
</style>
