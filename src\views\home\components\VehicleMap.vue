<template>
  <div class="map-container">
    <div ref="mapRef" class="amap-container"></div>

    <div class="map-controls">
      <div class="control-button zoom-in" @click="zoomIn">
        <i class="el-icon">+</i>
      </div>
      <div class="control-button zoom-out" @click="zoomOut">
        <i class="el-icon">-</i>
      </div>
      <div class="control-button refresh" @click="resetMap">
        <i class="el-icon">↻</i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineProps, defineEmits } from 'vue';
import mapService from '@/services/mapService';

// 设置AMap全局类型
declare global {
  interface Window {
    AMap: typeof AMap;
    _AMapSecurityConfig: {
      securityJsCode: string;
    };
  }
}

// 定义组件属性
const props = defineProps({
  vehiclesData: {
    type: Array,
    required: true
  }
});

// 定义组件事件
const emit = defineEmits(['vehicle-click']);

// 定义DOM引用
const mapRef = ref<HTMLElement | null>(null);

// 地图实例和标记集合
let map: AMap.Map | null = null;
const vehicleMarkers = new Map<string, { marker: AMap.Marker, textMarker: AMap.Text }>();
let infoWindow: AMap.InfoWindow | null = null;

// 初始化地图
const initMap = async () => {
  if (!mapRef.value) return;
  
  try {
    // 初始化高德地图
    map = await mapService.createMap(mapRef.value, {
      zoom: 12,
      center: [104.06, 30.67], // 成都市中心坐标
      mapStyle: 'amap://styles/dark'
    });
    
    // 创建信息窗口
    infoWindow = new window.AMap.InfoWindow({
      offset: new window.AMap.Pixel(0, -32),
      closeWhenClickMap: true
    });
    
    // 初始化标记
    initVehicleMarkers();
    
    // 添加点击事件
    map.on('click', () => {
      if (infoWindow) {
        infoWindow.close();
      }
    });
  } catch (error) {
    console.error('初始化地图失败:', error);
  }
};

// 初始化车辆标记
const initVehicleMarkers = () => {
  if (!map) return;
  
  // 清除已有标记
  vehicleMarkers.forEach(({ marker, textMarker }) => {
    map?.remove(marker);
    map?.remove(textMarker);
  });
  vehicleMarkers.clear();
  
  // 添加新标记
  props.vehiclesData.forEach((vehicle: any) => {
    addVehicleMarker(vehicle);
  });
};

// 添加车辆标记
const addVehicleMarker = (vehicle: any) => {
  if (!map) return;
  
  try {
    // 根据车辆状态确定标记图标
    const iconUrl = getVehicleIcon(vehicle.status);
    
    // 模拟随机位置（实际应用中应使用真实坐标）
    const position = [
      104.06 + (Math.random() * 0.1 - 0.05), 
      30.67 + (Math.random() * 0.1 - 0.05)
    ];
    
    // 创建标记
    const marker = new window.AMap.Marker({
      position: position,
      icon: new window.AMap.Icon({
        size: new window.AMap.Size(32, 32),
        image: iconUrl,
        imageSize: new window.AMap.Size(32, 32)
      }),
      anchor: 'bottom-center',
      offset: new window.AMap.Pixel(0, 0),
      zIndex: 100,
      extData: vehicle
    });
    
    // 创建车牌标记
    const textMarker = new window.AMap.Text({
      text: vehicle.id,
      position: position,
      anchor: 'center',
      offset: new window.AMap.Pixel(0, -35),
      style: {
        'background-color': 'rgba(0, 21, 41, 0.8)',
        'color': 'white',
        'border-radius': '4px',
        'border': '1px solid rgba(24, 144, 255, 0.5)',
        'padding': '2px 6px',
        'font-size': '12px',
        'white-space': 'nowrap'
      },
      zIndex: 110
    });
    
    // 添加到地图
    map.add(marker);
    map.add(textMarker);
    
    // 添加点击事件
    marker.on('click', (e: any) => {
      const vehicleData = e.target.getExtData();
      showVehicleInfoWindow(vehicleData, e.lnglat, marker);
      emit('vehicle-click', vehicleData, marker);
    });
    
    // 存储标记对象
    vehicleMarkers.set(vehicle.id, { marker, textMarker });
  } catch (error) {
    console.error('创建车辆标记失败:', error);
  }
};

// 显示车辆信息窗口
const showVehicleInfoWindow = (vehicle: any, position: any, marker: AMap.Marker) => {
  if (!infoWindow || !map) return;
  
  // 创建信息窗口内容
  const contentDiv = document.createElement('div');
  contentDiv.className = 'vehicle-info-window';
  contentDiv.style.width = '250px';
  contentDiv.style.background = 'linear-gradient(to bottom, #1a3a5f, #001529)';
  contentDiv.style.borderRadius = '8px';
  contentDiv.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.5)';
  contentDiv.style.padding = '8px';
  contentDiv.style.color = 'white';
  contentDiv.style.fontFamily = 'Microsoft YaHei, sans-serif';
  contentDiv.style.border = '1px solid rgba(24, 144, 255, 0.3)';
  contentDiv.style.animation = 'fadeIn 0.3s ease-out forwards';
  
  // 标题栏
  const titleDiv = document.createElement('div');
  titleDiv.style.display = 'flex';
  titleDiv.style.alignItems = 'center';
  titleDiv.style.marginBottom = '12px';
  
  const iconSpan = document.createElement('span');
  iconSpan.className = 'title-icon';
  iconSpan.innerHTML = '🚗';
  iconSpan.style.marginRight = '8px';
  iconSpan.style.fontSize = '18px';
  
  const titleSpan = document.createElement('span');
  titleSpan.textContent = `车辆 ${vehicle.id}`;
  titleSpan.style.fontWeight = 'bold';
  titleSpan.style.fontSize = '16px';
  titleSpan.style.flex = '1';
  
  const statusSpan = document.createElement('span');
  statusSpan.style.padding = '2px 6px';
  statusSpan.style.borderRadius = '4px';
  statusSpan.style.fontSize = '12px';
  
  if (vehicle.status === 'online') {
    statusSpan.textContent = '在线';
    statusSpan.style.backgroundColor = 'rgba(82, 196, 26, 0.2)';
    statusSpan.style.color = '#52c41a';
    statusSpan.style.border = '1px solid rgba(82, 196, 26, 0.3)';
  } else if (vehicle.status === 'charging') {
    statusSpan.textContent = '充电中';
    statusSpan.style.backgroundColor = 'rgba(250, 173, 20, 0.2)';
    statusSpan.style.color = '#faad14';
    statusSpan.style.border = '1px solid rgba(250, 173, 20, 0.3)';
  } else {
    statusSpan.textContent = '离线';
    statusSpan.style.backgroundColor = 'rgba(245, 34, 45, 0.2)';
    statusSpan.style.color = '#f5222d';
    statusSpan.style.border = '1px solid rgba(245, 34, 45, 0.3)';
  }
  
  titleDiv.appendChild(iconSpan);
  titleDiv.appendChild(titleSpan);
  titleDiv.appendChild(statusSpan);
  
  // 标题装饰线
  const decorationDiv = document.createElement('div');
  decorationDiv.style.height = '2px';
  decorationDiv.style.width = '100%';
  decorationDiv.style.background = 'linear-gradient(to right, rgba(24, 144, 255, 0.1), rgba(24, 144, 255, 0.8), rgba(24, 144, 255, 0.1))';
  decorationDiv.style.marginBottom = '12px';
  
  // 电量信息
  const batteryDiv = document.createElement('div');
  batteryDiv.style.marginBottom = '10px';
  batteryDiv.style.fontSize = '14px';
  batteryDiv.style.display = 'flex';
  batteryDiv.style.alignItems = 'center';
  
  const batteryLabel = document.createElement('span');
  batteryLabel.textContent = '电量: ';
  batteryLabel.style.marginRight = '8px';
  
  const progressContainer = document.createElement('div');
  progressContainer.style.flex = '1';
  progressContainer.style.height = '12px';
  progressContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.25)';
  progressContainer.style.borderRadius = '6px';
  progressContainer.style.overflow = 'hidden';
  progressContainer.style.border = '1px solid rgba(24, 144, 255, 0.3)';
  
  const progressBar = document.createElement('div');
  progressBar.style.height = '100%';
  progressBar.style.width = `${vehicle.battery}%`;
  
  // 根据电量设置颜色
  if (vehicle.battery <= 20) {
    progressBar.style.background = 'linear-gradient(to right, #f5222d, #ff7875)';
    progressBar.style.animation = 'pulse 1.5s infinite';
  } else if (vehicle.battery <= 50) {
    progressBar.style.background = 'linear-gradient(to right, #faad14, #ffc53d)';
  } else {
    progressBar.style.background = 'linear-gradient(to right, #52c41a, #95de64)';
  }
  
  const batteryText = document.createElement('span');
  batteryText.textContent = `${vehicle.battery}%`;
  batteryText.style.marginLeft = '8px';
  batteryText.style.color = vehicle.battery <= 20 ? '#ff7875' : 'white';
  
  progressContainer.appendChild(progressBar);
  batteryDiv.appendChild(batteryLabel);
  batteryDiv.appendChild(progressContainer);
  batteryDiv.appendChild(batteryText);
  
  // 速度信息
  const speedDiv = document.createElement('div');
  speedDiv.style.marginBottom = '10px';
  speedDiv.style.fontSize = '14px';
  
  speedDiv.textContent = `速度: ${vehicle.speed} km/h`;
  
  // 位置信息
  const locationDiv = document.createElement('div');
  locationDiv.style.marginBottom = '10px';
  locationDiv.style.fontSize = '14px';
  
  locationDiv.textContent = `位置: ${vehicle.location}`;
  
  // 按钮区域
  const buttonDiv = document.createElement('div');
  buttonDiv.style.display = 'flex';
  buttonDiv.style.justifyContent = 'space-between';
  buttonDiv.style.marginTop = '12px';
  
  const detailButton = document.createElement('div');
  detailButton.textContent = '查看详情';
  detailButton.style.padding = '6px 12px';
  detailButton.style.backgroundColor = 'rgba(24, 144, 255, 0.8)';
  detailButton.style.color = 'white';
  detailButton.style.borderRadius = '4px';
  detailButton.style.cursor = 'pointer';
  detailButton.style.textAlign = 'center';
  detailButton.style.flex = '1';
  detailButton.style.marginRight = '8px';
  detailButton.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
  detailButton.style.transition = 'all 0.3s ease';
  
  const trackButton = document.createElement('div');
  trackButton.textContent = '轨迹回放';
  trackButton.style.padding = '6px 12px';
  trackButton.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
  trackButton.style.color = 'white';
  trackButton.style.borderRadius = '4px';
  trackButton.style.cursor = 'pointer';
  trackButton.style.textAlign = 'center';
  trackButton.style.flex = '1';
  trackButton.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';
  trackButton.style.transition = 'all 0.3s ease';
  
  // 按钮悬停效果
  detailButton.onmouseover = function() {
    this.style.backgroundColor = 'rgba(24, 144, 255, 1)';
    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
  };
  detailButton.onmouseout = function() {
    this.style.backgroundColor = 'rgba(24, 144, 255, 0.8)';
    this.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.3)';
  };
  
  trackButton.onmouseover = function() {
    this.style.backgroundColor = 'rgba(0, 0, 0, 0.4)';
    this.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.4)';
  };
  trackButton.onmouseout = function() {
    this.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
    this.style.boxShadow = '0 2px 6px rgba(0, 0, 0, 0.2)';
  };
  
  // 按钮点击功能
  detailButton.onclick = function() {
    alert(`查看车辆 ${vehicle.id} 的详细信息`);
  };
  
  trackButton.onclick = function() {
    alert(`查看车辆 ${vehicle.id} 的轨迹回放`);
  };
  
  buttonDiv.appendChild(detailButton);
  buttonDiv.appendChild(trackButton);
  
  // 组合所有元素
  contentDiv.appendChild(titleDiv);
  contentDiv.appendChild(decorationDiv);
  contentDiv.appendChild(batteryDiv);
  contentDiv.appendChild(speedDiv);
  contentDiv.appendChild(locationDiv);
  contentDiv.appendChild(buttonDiv);
  
  // 设置信息窗口内容并打开
  infoWindow.setContent(contentDiv);
  infoWindow.open(map, position);
  
  // 添加CSS动画
  const style = document.createElement('style');
  style.textContent = `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes pulse {
      0% { opacity: 0.6; }
      50% { opacity: 1; }
      100% { opacity: 0.6; }
    }
  `;
  document.head.appendChild(style);
};

// 根据车辆状态获取图标
const getVehicleIcon = (status: string) => {
  switch (status) {
    case 'online':
      return '/icons/vehicle-online.png';
    case 'charging':
      return '/icons/vehicle-charging.png';
    case 'offline':
      return '/icons/vehicle-offline.png';
    default:
      return '/icons/vehicle-unknown.png';
  }
};

// 更新车辆在地图上的位置
const updateVehicleOnMap = (vehicleStatus: any) => {
  const { deviceName, licensePlate, lon, lat, batteryLevel, currentSpeed, chargingStatus } = vehicleStatus;
  
  // 尝试用设备名和车牌号查找
  const vehicleId = deviceName || licensePlate;
  if (!vehicleId || !map) return;
  
  // 检查是否已有标记
  const marker = vehicleMarkers.get(vehicleId);
  
  if (marker) {
    // 更新现有标记
    try {
      // 更新位置（如果提供了坐标）
      if (lon && lat) {
        const position = [parseFloat(lon), parseFloat(lat)];
        marker.marker.setPosition(position);
        marker.textMarker.setPosition(position);
      }
      
      // 更新图标（根据充电状态）
      const iconUrl = getVehicleIcon(chargingStatus ? 'charging' : 'online');
      marker.marker.setIcon(new window.AMap.Icon({
        size: new window.AMap.Size(32, 32),
        image: iconUrl,
        imageSize: new window.AMap.Size(32, 32)
      }));
      
      // 更新扩展数据
      const extData = marker.marker.getExtData();
      marker.marker.setExtData({
        ...extData,
        battery: batteryLevel,
        speed: currentSpeed,
        status: chargingStatus ? 'charging' : 'online'
      });
    } catch (error) {
      console.error("更新车辆标记失败:", error);
    }
  } else {
    // 添加新标记
    // 创建车辆对象
    const vehicle = {
      id: vehicleId,
      status: chargingStatus ? 'charging' : 'online',
      battery: batteryLevel,
      speed: currentSpeed,
      location: '实时定位中'
    };
    
    // 添加到地图上
    addVehicleMarker(vehicle);
  }
};

// 放大地图
const zoomIn = () => {
  if (map) {
    map.setZoom(map.getZoom() + 1);
  }
};

// 缩小地图
const zoomOut = () => {
  if (map) {
    map.setZoom(map.getZoom() - 1);
  }
};

// 重置地图
const resetMap = () => {
  if (map) {
    map.setZoom(12);
    map.setCenter([104.06, 30.67]); // 成都市中心坐标
  }
};

// 组件挂载时初始化地图
onMounted(() => {
  initMap();
});

// 组件卸载时销毁地图和标记
onBeforeUnmount(() => {
  if (map) {
    vehicleMarkers.forEach(({ marker, textMarker }) => {
      marker.remove();
      textMarker.remove();
    });
    vehicleMarkers.clear();
    map.destroy();
    map = null;
  }
});

// 导出方法供父组件调用
defineExpose({
  updateVehicleOnMap
});
</script>

<style lang="less" scoped>
.map-container {
  position: relative;
  width: 100%;
  height: 600px;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid rgba(24, 144, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.amap-container {
  width: 100%;
  height: 100%;
}

.map-controls {
  position: absolute;
  right: 15px;
  top: 15px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-button {
  width: 32px;
  height: 32px;
  background-color: rgba(0, 21, 41, 0.8);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  cursor: pointer;
  font-size: 18px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  
  &:hover {
    background-color: rgba(24, 144, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
  }
}

// 对高德地图的全局样式重写
:global(.amap-info-content) {
  background: linear-gradient(to bottom, #1a3a5f, #001529) !important;
  color: white !important;
  padding: 0 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
  border: 1px solid rgba(24, 144, 255, 0.3) !important;
}

:global(.amap-info-close) {
  color: white !important;
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-radius: 50% !important;
  opacity: 0.8 !important;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.5) !important;
    opacity: 1 !important;
  }
}

:global(.amap-info-sharp) {
  background-color: #001529 !important;
}
</style> 