/* 深蓝色主题 - 模仿Tailwind示例风格 */

html.deep-blue-theme,
body.deep-blue-theme,
.deep-blue-theme {
  /* 基础背景和文本颜色 */
  --el-color-primary: #409EFF;
  --el-color-primary-light-3: #79bbff;
  --el-color-primary-light-5: #a6d2ff;
  --el-color-primary-light-7: #d9ecff;
  --el-color-primary-light-8: #ecf5ff;
  --el-color-primary-light-9: #f5f7fa;
  --el-color-primary-dark-2: #337ecc;

  /* 背景颜色 */
  --el-bg-color: #0A1F3D;
  --el-bg-color-page: #0A1F3D;
  --el-bg-color-overlay: #162C4A;
  
  /* 文本颜色 */
  --el-text-color-primary: #FFFFFF;
  --el-text-color-regular: #E5E7EB;
  --el-text-color-secondary: #A0AEC0;
  --el-text-color-placeholder: #718096;
  --el-text-color-disabled: #4A5568;
  
  /* 边框颜色 */
  --el-border-color: rgba(255, 255, 255, 0.1);
  --el-border-color-light: rgba(255, 255, 255, 0.08);
  --el-border-color-lighter: rgba(255, 255, 255, 0.06);
  --el-border-color-extra-light: rgba(255, 255, 255, 0.04);
  
  /* 填充颜色 */
  --el-fill-color: rgba(255, 255, 255, 0.05);
  --el-fill-color-light: rgba(255, 255, 255, 0.02);
  --el-fill-color-lighter: rgba(255, 255, 255, 0.01);
  --el-fill-color-blank: #162C4A;
  
  /* 遮罩和阴影 */
  --el-mask-color: rgba(10, 31, 61, 0.8);
  --el-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  --el-box-shadow-light: 0 1px 6px 0 rgba(0, 0, 0, 0.2);
}

/* 全局样式 */
body.deep-blue-theme {
  background-color: #0A1F3D;
  color: #FFFFFF;
}

/* 应用整体容器样式 */
.deep-blue-theme .rr {
  background-color: #0A1F3D;
  color: #FFFFFF;
}

/* 侧边栏样式 */
.deep-blue-theme .rr-sidebar {
  background-color: #162C4A;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.deep-blue-theme .rr-sidebar-menu {
  background-color: transparent;
  border-right: none;
}

/* 头部样式 */
.deep-blue-theme .rr-header {
  background-color: #0A1F3D;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.deep-blue-theme .rr-header-ctx {
  background-color: #0A1F3D;
}

/* 主内容区样式 */
.deep-blue-theme .rr-view-container {
  background-color: #0A1F3D;
}

.deep-blue-theme .rr-view {
  background-color: #0A1F3D;
}

/* 卡片样式 */
.deep-blue-theme .el-card {
  background-color: #162C4A;
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

.deep-blue-theme .el-card__header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 表格样式 */
.deep-blue-theme .el-table {
  background-color: transparent !important;
}

.deep-blue-theme .el-table th, 
.deep-blue-theme .el-table tr {
  background-color: transparent !important;
  color: white !important;
}

.deep-blue-theme .el-table td {
  border-bottom-color: rgba(255, 255, 255, 0.1) !important;
}

.deep-blue-theme .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* 表单元素样式 */
.deep-blue-theme .el-input__wrapper {
  background-color: rgba(255, 255, 255, 0.05) !important;
  box-shadow: none !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.deep-blue-theme .el-input__inner {
  color: white !important;
}

.deep-blue-theme .el-select__popper {
  background-color: #162C4A !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.deep-blue-theme .el-select-dropdown__item {
  color: #E5E7EB !important;
}

.deep-blue-theme .el-select-dropdown__item.hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.deep-blue-theme .el-select-dropdown__item.selected {
  background-color: #409EFF !important;
  color: white !important;
}

/* 按钮样式 */
.deep-blue-theme .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
}

.deep-blue-theme .el-button--primary:hover,
.deep-blue-theme .el-button--primary:focus {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

/* 对话框样式 */
.deep-blue-theme .el-dialog {
  background-color: #162C4A !important;
}

.deep-blue-theme .el-dialog__title {
  color: white !important;
}

.deep-blue-theme .el-dialog__body {
  color: #E5E7EB !important;
}

/* 菜单样式 */
.deep-blue-theme .el-menu {
  background-color: transparent;
  border-right: none;
}

.deep-blue-theme .el-menu-item {
  color: #E5E7EB;
}

.deep-blue-theme .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.deep-blue-theme .el-menu-item.is-active {
  color: #FFFFFF;
  background-color: #409EFF;
}

.deep-blue-theme .el-sub-menu__title {
  color: #E5E7EB;
}

/* 下拉菜单样式 */
.deep-blue-theme .el-dropdown-menu {
  background-color: #162C4A;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.deep-blue-theme .el-dropdown-menu__item {
  color: #E5E7EB;
}

.deep-blue-theme .el-dropdown-menu__item:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #FFFFFF;
}

/* 弹出提示样式 */
.deep-blue-theme .el-message {
  background-color: #162C4A;
  border-color: rgba(255, 255, 255, 0.1);
}

.deep-blue-theme .el-message__content {
  color: #E5E7EB;
}

/* 加载遮罩样式 */
.deep-blue-theme .el-loading-mask {
  background-color: rgba(10, 31, 61, 0.8);
}

/* 标签页样式 */
.deep-blue-theme .el-tabs__item {
  color: #A0AEC0;
}

.deep-blue-theme .el-tabs__item.is-active {
  color: #409EFF;
}

.deep-blue-theme .el-tabs__active-bar {
  background-color: #409EFF;
}

/* 树形控件样式 */
.deep-blue-theme .el-tree {
  background-color: transparent;
  color: #E5E7EB;
}

.deep-blue-theme .el-tree-node__content:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.deep-blue-theme .el-tree-node.is-current > .el-tree-node__content {
  background-color: rgba(64, 158, 255, 0.2);
  color: #409EFF;
} 