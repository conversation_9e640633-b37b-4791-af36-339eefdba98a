<template>
  <div class="mod-task__patroltask" ref="containerRef">
    <!-- 顶部统计信息区域 -->
    <div class="stats-container">
      <div class="stat-card">
        <div class="stat-title">总任务数</div>
        <div class="stat-value">{{ statsData.totalTasks || 0 }}</div>
        <div class="stat-trend" :class="{ up: true }">
          <el-icon><TopRight /></el-icon> {{ statsData.totalTasksChange || 0 }}% 较上周
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-title">进行中任务</div>
        <div class="stat-value">{{ statsData.inProgressTasks || 0 }}</div>
        <div class="stat-trend" :class="{ up: true }">
          <el-icon><TopRight /></el-icon> {{ statsData.inProgressTasksChange || 0 }}%
          较上周
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-title">已完成任务</div>
        <div class="stat-value">{{ statsData.completedTasks || 0 }}</div>
        <div class="stat-trend" :class="{ up: true }">
          <el-icon><TopRight /></el-icon> {{ statsData.completedTasksChange || 0 }}%
          较上周
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-title">任务完成率</div>
        <div class="stat-value">{{ statsData.completionRate || 0 }}%</div>
        <div class="stat-trend" :class="{ up: true }">
          <el-icon><TopRight /></el-icon> {{ statsData.completionRateChange || 0 }}%
          较上周
        </div>
      </div>
    </div>

    <!-- 搜索和操作区域 -->
    <div class="operation-container">
      <el-input
        v-model="searchQuery"
        placeholder="搜索任务..."
        class="search-input"
        clearable
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
      <div class="form-right">
        <el-button type="primary" @click="addOrUpdateHandle()" class="add-button">
          <span
            class="iconfont icon-xinjianrenwu-fanbai"
            style="margin-right: 5px"
          ></span>
          新建任务
        </el-button>
        <el-button type="danger" @click="state.deleteHandle()">
          <span class="iconfont icon-quxiaorenwu" style="margin-right: 5px"></span
          >取消任务
        </el-button>
      </div>
    </div>

    <!-- 任务卡片网格 -->
    <div class="task-grid" v-loading="state.dataListLoading">
      <div
        v-for="task in state.dataList"
        :key="task.id"
        class="task-card"
        :class="getTaskStatusClass(task.taskStatus)"
      >
        <div class="card-border-highlight"></div>
        <div class="task-corner-badge" v-if="isRecentTask(task)">NEW</div>
        <div class="card-header">
          <span class="task-title">{{ task.taskName }}</span>
          <el-tag
            size="small"
            :type="getTaskStatusType(task.taskStatus)"
            class="task-status"
            effect="dark"
          >
            {{ task.taskStatus }}
          </el-tag>
        </div>
        <div class="card-content">
          <div class="task-info">
            <div class="info-row">
              <span class="info-label">编号:</span> {{ task.taskNumber }}
            </div>
            <div class="info-row">
              <span class="info-label">执行车辆:</span> {{ task.vehicleIds }}
            </div>
            <div class="info-divider"></div>
            <div class="info-row">
              <span class="info-label">开始时间:</span>
              {{ formatDateTime(task.startTime) }}
            </div>
            <div class="info-row">
              <span class="info-label">预计完成:</span> {{ formatDateTime(task.endTime) }}
            </div>
          </div>
        </div>
        <div class="task-progress">
          <el-progress
            :percentage="getTaskProgress(task)"
            :status="getProgressStatus(task.taskStatus)"
            :stroke-width="8"
            :show-text="false"
          ></el-progress>
          <span class="progress-text">完成进度: {{ getTaskProgress(task) }}%</span>
        </div>
        <div class="card-footer">
          <el-button-group>
            <el-button type="primary" size="small" @click="viewTaskDetails(task)">
              <el-icon><View /></el-icon>
            </el-button>
            <el-button type="primary" size="small" @click="addOrUpdateHandle(task.id)">
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button type="danger" size="small" @click="state.deleteHandle(task.id)">
              <el-icon><Delete /></el-icon>
            </el-button>
          </el-button-group>
        </div>
        <div class="task-timestamp">
          <div v-if="task.createdAt">创建于 {{ formatDateTime(task.createdAt) }}</div>
          <div v-if="task.updatedAt">更新于 {{ formatDateTime(task.updatedAt) }}</div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="state.page"
        :page-sizes="[1, 2]"
        :page-size="state.limit"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList"
      >确定</add-or-update
    >
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import {
  reactive,
  ref,
  toRefs,
  onMounted,
  computed,
  defineAsyncComponent,
  nextTick,
} from "vue";
import { ElNotification } from "element-plus";
// 使用异步组件导入方式
const AddOrUpdate = defineAsyncComponent(() => import("./patroltask-add-or-update.vue"));

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/task/patroltask/page",
  getDataListIsPage: true,
  exportURL: "/task/patroltask/export",
  deleteURL: "/task/patroltask",
});

const state = reactive({ ...useView(view), ...toRefs(view) });

// 搜索查询
const searchQuery = ref("");

// 统计数据
const statsData = reactive({
  totalTasks: 128,
  totalTasksChange: 12,
  inProgressTasks: 45,
  inProgressTasksChange: 8,
  completedTasks: 76,
  completedTasksChange: 15,
  completionRate: 89,
  completionRateChange: 5,
});

// 添加或修改任务
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

// 查看任务详情
const viewTaskDetails = (task: any) => {
  // 实现任务详情查看逻辑
  console.log("查看任务详情:", task);
};

// 获取任务进度
const getTaskProgress = (task: any) => {
  // 这里应该根据实际数据计算进度
  if (task.progress) {
    return parseInt(task.progress);
  }

  // 如果没有进度数据，根据任务状态返回默认值
  switch (task.taskStatus) {
    case "未开始":
      return 0;
    case "进行中":
      return 45;
    case "已完成":
      return 100;
    case "已取消":
      return 0;
    default:
      return 0;
  }
};

// 获取进度条状态
const getProgressStatus = (taskStatus: string) => {
  switch (taskStatus) {
    case "进行中":
      return "";
    case "已完成":
      return "success";
    case "已取消":
      return "exception";
    default:
      return "info";
  }
};

// 获取任务状态类型
const getTaskStatusType = (taskStatus: string) => {
  switch (taskStatus) {
    case "未开始":
      return "info";
    case "进行中":
      return "primary";
    case "已完成":
      return "success";
    case "已取消":
      return "danger";
    default:
      return "info";
  }
};

// 获取任务卡片的状态类
const getTaskStatusClass = (taskStatus: string) => {
  switch (taskStatus) {
    case "未开始":
      return "status-pending";
    case "进行中":
      return "status-progress";
    case "已完成":
      return "status-completed";
    case "已取消":
      return "status-canceled";
    default:
      return "";
  }
};

// 加载统计数据
const loadStatsData = () => {
  // 实际应用中应该通过API获取统计数据
  console.log("加载统计数据");
};

// 判断是否为最近创建的任务
const isRecentTask = (task: any) => {
  if (!task.createdAt && !task.createTime) return false;

  // 使用createdAt或createTime字段，根据实际后端字段名
  const taskDate = new Date(task.createdAt || task.createTime || task.updateTime);
  const now = new Date();
  const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24小时前

  return taskDate > oneDayAgo;
};

// 格式化日期为北京时间(UTC+8)
const formatDate = (dateStr: string) => {
  if (!dateStr) return "-";
  try {
    // 创建日期对象并获取时间戳
    const timestamp = new Date(dateStr).getTime();

    // 如果日期无效，则抛出错误
    if (isNaN(timestamp)) {
      throw new Error("Invalid date");
    }

    // 创建UTC+8时区的日期对象(北京时间)
    const beijingTimestamp = timestamp + 8 * 60 * 60 * 1000; // 加8小时的毫秒数
    const beijingDate = new Date(beijingTimestamp);

    // 格式化为 YYYY-MM-DD HH:MM:SS
    const year = beijingDate.getUTCFullYear();
    const month = (beijingDate.getUTCMonth() + 1).toString().padStart(2, "0");
    const day = beijingDate.getUTCDate().toString().padStart(2, "0");
    const hours = beijingDate.getUTCHours().toString().padStart(2, "0");
    const minutes = beijingDate.getUTCMinutes().toString().padStart(2, "0");
    const seconds = beijingDate.getUTCSeconds().toString().padStart(2, "0");

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (e) {
    console.error("Date formatting error:", e);
    return dateStr;
  }
};
// 格式化日期时间为本地时间格式
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return "";

  try {
    // 创建日期对象 - 会自动转换为本地时区
    const date = new Date(dateTimeStr);

    // 格式化年月日
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const day = date.getDate().toString().padStart(2, "0");

    // 格式化时分秒
    const hours = date.getHours().toString().padStart(2, "0");
    const minutes = date.getMinutes().toString().padStart(2, "0");
    const seconds = date.getSeconds().toString().padStart(2, "0");

    // 组合成最终格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error("日期格式化错误:", error);
    return dateTimeStr;
  }
};
// 容器引用
const containerRef = ref<HTMLElement | null>(null);

// 处理页码变化
const handlePageChanged = () => {
  // 当页码变化时，滚动到顶部
  nextTick(() => {
    if (containerRef.value) {
      containerRef.value.scrollTop = 0;
    }
  });
};

// 监听分页事件
const handleSizeChange = (size: number) => {
  state.pageSizeChangeHandle(size);
  handlePageChanged();
};

const handleCurrentChange = (page: number) => {
  state.pageCurrentChangeHandle(page);
  handlePageChanged();
};

// 设置初始页面大小
onMounted(() => {
  loadStatsData();
  // 设置每页显示10个卡片
  state.limit = 10;
});
</script>

<style lang="less" scoped>
// Element Plus 图标全局样式
:deep(.el-icon) {
  display: inline-flex;
  vertical-align: middle;
  &.el-button__icon {
    margin-right: 4px;
  }
}

.mod-task__patroltask {
  height: 92vh;
  width: 100%;
  padding: 16px;
  background-color: #001529;
  color: white;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-y: auto;

  // 统计卡片容器
  .stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 20px;

    .stat-card {
      background: linear-gradient(135deg, rgba(0, 21, 41, 0.8), rgba(0, 30, 60, 0.7));
      border: 1px solid rgba(24, 144, 255, 0.2);
      border-radius: 8px;
      padding: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

      .stat-title {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.7);
        margin-bottom: 8px;
      }

      .stat-value {
        font-size: 28px;
        font-weight: 600;
        margin-bottom: 12px;
      }

      .stat-trend {
        font-size: 12px;
        display: flex;
        align-items: center;

        &.up {
          color: #52c41a;
        }

        &.down {
          color: #ff4d4f;
        }
      }
    }
  }

  // 操作区域
  .operation-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .search-input {
      width: 300px;
    }

    .form-right {
      display: flex;
      gap: 12px;

      .add-button {
        position: relative;
        overflow: hidden;

        .el-icon + span {
          margin-left: 4px;
        }

        &::after {
          content: "";
          position: absolute;
          top: -50%;
          left: -50%;
          width: 200%;
          height: 200%;
          background: rgba(255, 255, 255, 0.2);
          transform: rotate(45deg);
          opacity: 0;
          transition: opacity 0.6s;
        }

        &:hover::after {
          opacity: 1;
          animation: button-shine 1.5s;
        }
      }
    }
  }

  // 任务卡片网格
  .task-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
    flex: 1;
    overflow-y: auto;
    margin-bottom: 48px;

    @media (max-width: 1600px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (max-width: 1200px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (max-width: 992px) {
      grid-template-columns: repeat(2, 1fr);
    }

    @media (max-width: 576px) {
      grid-template-columns: 1fr;
    }

    .task-card {
      background: linear-gradient(135deg, rgba(0, 21, 41, 0.8), rgba(0, 30, 60, 0.7));
      border: 1px solid rgba(24, 144, 255, 0.2);
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      position: relative;
      min-height: 220px;
      max-height: 280px;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.35);
        border-color: rgba(24, 144, 255, 0.5);
      }

      .card-border-highlight {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 4px;
        background: #ff4d4f;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      &:hover .card-border-highlight {
        opacity: 1;
      }

      &.status-pending {
        border-left: 4px solid #909399;

        .card-border-highlight {
          background: #909399;
        }
      }

      &.status-progress {
        border-left: 4px solid #1890ff;

        .card-border-highlight {
          background: #1890ff;
        }
      }

      &.status-completed {
        border-left: 4px solid #52c41a;

        .card-border-highlight {
          background: #52c41a;
        }
      }

      &.status-canceled {
        border-left: 4px solid #ff4d4f;

        .card-border-highlight {
          background: #ff4d4f;
        }
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 12px;
        background: rgba(0, 0, 0, 0.3);
        border-bottom: 1px solid rgba(24, 144, 255, 0.2);

        .task-title {
          font-size: 14px;
          font-weight: 500;
          color: white;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          max-width: 70%;
        }

        .task-status {
          text-transform: uppercase;
          font-size: 12px;
          padding: 2px 8px;
          font-weight: 500;
        }
      }

      .card-content {
        padding: 10px 12px;
        flex: 1;
        overflow-y: auto;

        .task-info {
          .info-row {
            margin: 4px 0;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.85);
            display: flex;
            line-height: 1.4;

            .info-label {
              color: rgba(255, 255, 255, 0.6);
              width: 70px;
              flex-shrink: 0;
            }
          }

          .info-divider {
            height: 1px;
            background: rgba(255, 255, 255, 0.1);
            margin: 6px 0;
          }
        }
      }

      // 右上角标记
      .task-corner-badge {
        position: absolute;
        top: 0;
        right: 0;
        background: #f56c6c;
        color: white;
        font-size: 10px;
        font-weight: bold;
        padding: 2px 8px;
        border-radius: 0 8px 0 8px;
        z-index: 2;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        animation: pulse 1.5s infinite;
      }

      .task-progress {
        padding: 0 12px 12px;
        position: relative;

        .progress-text {
          display: block;
          margin-bottom: 8px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          text-align: right;
        }

        // 自定义进度条样式
        :deep(.el-progress-bar__inner) {
          transition: all 0.8s ease;
        }

        :deep(.el-progress-bar__outer) {
          background-color: rgba(255, 255, 255, 0.1);
        }
      }

      .card-footer {
        padding: 10px 12px;
        border-top: 1px solid rgba(24, 144, 255, 0.2);
        display: flex;
        justify-content: center;
        background: rgba(0, 0, 0, 0.2);

        .el-button-group {
          width: 100%;
          display: flex;
          justify-content: space-between;

          .el-button {
            flex: 1;
            padding: 6px 8px;
            font-size: 12px;

            .el-icon {
              margin-right: 0;
              font-size: 14px;
              vertical-align: middle;
            }
          }
        }
      }

      // 时间戳
      .task-timestamp {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.5);
        text-align: right;
        padding: 6px;
        background: rgba(0, 0, 0, 0.15);
      }
    }
  }

  // 分页容器
  .pagination-container {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 12px 0;
    margin-top: 16px;
    display: flex;
    justify-content: center;
    background: rgba(0, 21, 41, 0.9);
    backdrop-filter: blur(5px);
    border-radius: 0;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
  }

  // 动画
  @keyframes pulse {
    0% {
      transform: scale(0.9);
    }
    50% {
      transform: scale(1);
    }
    100% {
      transform: scale(0.9);
    }
  }

  @keyframes button-shine {
    0% {
      left: -50%;
      opacity: 0;
    }
    50% {
      opacity: 0.5;
    }
    100% {
      left: 120%;
      opacity: 0;
    }
  }
}
</style>
