<template>
  <div class="alarm-page">
    <div class="page-header">
      <div class="system-title">
        <i class="el-icon-data-analysis"></i>
        <h1>智能宣传与告警响应系统</h1>
      </div>
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态</span>
          <el-tag type="success" effect="dark">正常运行</el-tag>
        </div>
        <div class="status-item">
          <span class="status-label">在线设备</span>
          <el-tag type="info" effect="dark">3台</el-tag>
        </div>
        <div class="status-item">
          <span class="status-label">当前时间</span>
          <span class="status-value">{{ currentTime }}</span>
        </div>
      </div>
    </div>

    <div class="main-content">
      <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
        <el-tab-pane name="publicity">
          <template #label>
            <div class="custom-tab-label">
              <i class="el-icon-megaphone"></i>
              <span>宣传互动</span>
            </div>
          </template>
          <PublicityInteraction />
        </el-tab-pane>
        <el-tab-pane name="alarm">
          <template #label>
            <div class="custom-tab-label">
              <i class="el-icon-warning-outline"></i>
              <span>一键告警响应</span>
              <el-badge v-if="hasNewAlarm" value="新" class="alarm-badge" />
            </div>
          </template>
          <AlarmResponse @alarm-triggered="onAlarmTriggered" />
        </el-tab-pane>
        <el-tab-pane name="illegal">
          <template #label>
            <div class="custom-tab-label">
              <i class="el-icon-video-camera"></i>
              <span>违法行为监控</span>
              <el-badge v-if="hasNewIllegalAlert" value="新" class="alarm-badge" type="danger" />
            </div>
          </template>
          <IllegalActivity @alert-detected="onIllegalAlertDetected" />
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="page-footer">
      <div class="footer-info">
        <span>© 2023 智能宣传车系统</span>
        <span>|</span>
        <span>版本 v1.0.2</span>
      </div>
      <div class="quick-links">
        <el-button type="text" size="small">用户手册</el-button>
        <el-button type="text" size="small">系统设置</el-button>
        <el-button type="text" size="small">关于我们</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import PublicityInteraction from './PublicityInteraction.vue';
import AlarmResponse from './AlarmResponse.vue';
import IllegalActivity from './IllegalActivity.vue';

const activeTab = ref('publicity');
const hasNewAlarm = ref(false);
const hasNewIllegalAlert = ref(false);
const currentTime = ref('');
let timerID: number | null = null;

// 格式化当前时间
function updateCurrentTime() {
  const now = new Date();
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  };
  currentTime.value = new Intl.DateTimeFormat('zh-CN', options).format(now);
}

// 处理标签页点击
function handleTabClick() {
  if (activeTab.value === 'alarm') {
    hasNewAlarm.value = false;
  }
}

// 处理告警触发
function onAlarmTriggered() {
  if (activeTab.value !== 'alarm') {
    hasNewAlarm.value = true;
  }
}

// 处理违法行为检测
function onIllegalAlertDetected() {
  hasNewIllegalAlert.value = true;
}

onMounted(() => {
  // 更新当前时间
  updateCurrentTime();
  timerID = window.setInterval(updateCurrentTime, 1000);
});

onUnmounted(() => {
  if (timerID !== null) {
    clearInterval(timerID);
  }
});
</script>

<style scoped>
.alarm-page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f2f6fc;
}

.page-header {
  background: linear-gradient(90deg, #001529 0%, #003a70 100%);
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
}

.system-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.system-title h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.system-status {
  display: flex;
  gap: 20px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
}

.status-value {
  font-size: 14px;
  font-weight: 500;
}

.main-content {
  flex: 1;
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

:deep(.el-tabs--border-card) {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-tabs__header) {
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.custom-tab-label {
  display: flex;
  align-items: center;
  gap: 5px;
  position: relative;
  padding: 0 5px;
}

.alarm-badge {
  margin-left: 5px;
}

.page-footer {
  background: #001529;
  color: rgba(255, 255, 255, 0.7);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.footer-info {
  display: flex;
  gap: 10px;
}

.quick-links {
  display: flex;
  gap: 15px;
}

:deep(.el-button--text) {
  color: rgba(255, 255, 255, 0.7);
}

:deep(.el-button--text:hover) {
  color: white;
}
</style> 