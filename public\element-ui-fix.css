/* Element UI菜单组件样式修复 */

/* 全局覆盖Element UI的菜单样式 */
.el-menu {
  --el-menu-bg-color: #0A1F3D !important;
  --el-menu-text-color: white !important;
  --el-menu-hover-bg-color: #1D68C1 !important;
  --el-menu-active-color: white !important;
  border-right: none !important;
}

/* 父级菜单项 */
.el-menu-item,
.el-sub-menu__title {
  color: white !important;
  background-color: #0A1F3D !important;
}

/* 子菜单 */
.el-menu--inline,
.el-menu--inline .el-menu-item {
  background-color: #071A33 !important;
  color: white !important;
}

/* 激活状态 */
.el-menu-item.is-active {
  background-color: #1D68C1 !important;
  color: white !important;
  border-left: 3px solid white !important;
}

/* 图标颜色 */
.el-menu-item i,
.el-sub-menu__title i,
.el-sub-menu__icon-arrow {
  color: white !important;
}

/* SVG图标颜色 */
.el-menu-item svg,
.el-sub-menu__title svg,
.el-menu-item svg path,
.el-sub-menu__title svg path {
  fill: white !important;
  color: white !important;
}

/* 弹出式子菜单 */
.el-menu--popup {
  background-color: #071A33 !important;
}

.el-menu--popup .el-menu-item {
  background-color: #071A33 !important;
  color: white !important;
}

/* 登录表单前缀图标例外处理 */
.rr-login-right .el-input__prefix i,
.rr-login-right .el-input__prefix .el-icon,
.rr-login-right .el-input__prefix-inner .el-icon,
.rr-login-right .el-input__prefix span i {
  color: #333 !important;
}

.rr-login-right .el-input__prefix i svg,
.rr-login-right .el-input__prefix .el-icon svg,
.rr-login-right .el-input__prefix span i svg,
.rr-login-right .el-input__prefix svg path {
  fill: #333 !important;
  color: #333 !important;
} 