// 定义高德地图的接口类型
interface AMapOptions {
  zoom?: number;
  center?: [number, number];
  mapStyle?: string;
  viewMode?: string;
  pitchEnable?: boolean;
  [key: string]: any;
}

/**
 * 地图服务，用于处理高德地图相关功能
 */
class MapService {
  private mapScriptLoaded: boolean = false;
  private loadPromise: Promise<void> | null = null;

  /**
   * 加载高德地图脚本
   */
  private loadMapScript(): Promise<void> {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = new Promise((resolve, reject) => {
      if (window.AMap) {
        this.mapScriptLoaded = true;
        resolve();
        return;
      }

      // 设置安全密钥（实际项目中应该从环境变量中获取）
      window._AMapSecurityConfig = {
        securityJsCode: 'YOUR_SECURITY_CODE' // 替换为实际的安全密钥
      };

      // 创建脚本标签
      const script = document.createElement('script');
      script.type = 'text/javascript';
      script.async = true;
      script.src = 'https://webapi.amap.com/maps?v=2.0&key=YOUR_API_KEY'; // 替换为实际的API密钥
      
      // 监听加载事件
      script.onload = () => {
        this.mapScriptLoaded = true;
        resolve();
      };
      
      script.onerror = () => {
        reject(new Error('高德地图脚本加载失败'));
      };
      
      // 添加到文档
      document.head.appendChild(script);
    });

    return this.loadPromise;
  }

  /**
   * 创建地图实例
   * @param container 地图容器元素
   * @param options 地图选项
   */
  async createMap(container: HTMLElement, options: AMapOptions = {}): Promise<AMap.Map> {
    await this.loadMapScript();
    
    const defaultOptions: AMapOptions = {
      zoom: 11,
      center: [116.397428, 39.90923], // 默认北京中心点
      mapStyle: 'amap://styles/normal',
      viewMode: '2D',
      pitchEnable: false
    };
    
    // 合并选项
    const mergedOptions = { ...defaultOptions, ...options };
    
    // 创建地图实例
    return new window.AMap.Map(container, mergedOptions);
  }

  /**
   * 获取地址的经纬度
   * @param address 地址字符串
   */
  async getLocationByAddress(address: string): Promise<[number, number] | null> {
    await this.loadMapScript();
    
    return new Promise((resolve, reject) => {
      window.AMap.plugin('AMap.Geocoder', () => {
        const geocoder = new window.AMap.Geocoder();
        
        geocoder.getLocation(address, (status: string, result: any) => {
          if (status === 'complete' && result.info === 'OK') {
            const location = result.geocodes[0].location;
            resolve([location.lng, location.lat]);
          } else {
            resolve(null);
          }
        });
      });
    });
  }

  /**
   * 根据经纬度获取地址信息
   * @param location 经纬度坐标
   */
  async getAddressByLocation(location: [number, number]): Promise<string | null> {
    await this.loadMapScript();
    
    return new Promise((resolve, reject) => {
      window.AMap.plugin('AMap.Geocoder', () => {
        const geocoder = new window.AMap.Geocoder();
        
        geocoder.getAddress(location, (status: string, result: any) => {
          if (status === 'complete' && result.info === 'OK') {
            resolve(result.regeocode.formattedAddress);
          } else {
            resolve(null);
          }
        });
      });
    });
  }

  /**
   * 计算两点之间的距离（米）
   * @param start 起点坐标
   * @param end 终点坐标
   */
  async calculateDistance(start: [number, number], end: [number, number]): Promise<number> {
    await this.loadMapScript();
    
    const startLngLat = new window.AMap.LngLat(start[0], start[1]);
    const endLngLat = new window.AMap.LngLat(end[0], end[1]);
    
    return startLngLat.distance(endLngLat);
  }
}

// 导出单例
export default new MapService(); 