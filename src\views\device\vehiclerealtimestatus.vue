<template>
  <div class="mod-device__vehiclerealtimestatus">
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column label="序号" header-align="center" align="center" width="60">
                <template v-slot="scope">
                  {{ scope.$index + 1 + ((state.page || 1) - 1) * (state.limit || 10) }}
                </template>
              </el-table-column>
              <el-table-column prop="licensePlate" label="车牌号" header-align="center" align="center"></el-table-column>
              <el-table-column prop="location" label="位置" header-align="center" align="center"></el-table-column>
              <el-table-column prop="status" label="状态" header-align="center" align="center">
                <template v-slot="scope">
                  <div class="status-display" :class="'status-' + getStatusClass(scope.row.status)">
                    <span v-if="scope.row.status === '在线'" class="dot online"></span>
                    <span v-else-if="scope.row.status === '离线'" class="dot offline"></span>
                    <span v-else-if="scope.row.status === '故障'" class="dot error"></span>
                    <span v-else-if="scope.row.status === '充电'" class="dot charging"></span>
                    <span v-else class="dot unknown"></span>
                    <span class="status-text">{{ scope.row.status }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="batteryLevel" label="电量百分比" header-align="center" align="center">
                <template v-slot="scope">
                  <el-progress 
                    :percentage="Number(scope.row.batteryLevel)" 
                    :color="getBatteryColor(scope.row.batteryLevel)"
                    :stroke-width="15"
                    :show-text="true"
                    :text-inside="true"
                    style="min-width: 100px;"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="currentSpeed" label="速度(km/h)" header-align="center" align="center"></el-table-column>
              <el-table-column prop="lastHeartbeat" label="记录更新时间" header-align="center" align="center">
                <template v-slot="scope">
                  {{ formatDateTime(scope.row.lastHeartbeat) }}
                </template>
              </el-table-column>
            <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" circle size="small" @click="addOrUpdateHandle(scope.row.id)">
            <el-icon><Edit /></el-icon>
          </el-button>
          <el-button type="danger" circle size="small" @click="state.deleteHandle(scope.row.id)">
            <el-icon><Delete /></el-icon>
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./vehiclerealtimestatus-add-or-update.vue";
import { Edit, Delete } from '@element-plus/icons-vue';
import { 
  CircleCheck, 
  WarningFilled, 
  CircleCloseFilled, 
  Lightning, 
  InfoFilled 
} from '@element-plus/icons-vue';

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/device/vehiclerealtimestatus/page",
  getDataListIsPage: true,
  exportURL: "/device/vehiclerealtimestatus/export",
  deleteURL: "/device/vehiclerealtimestatus"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return '';
  
  try {
    // 创建日期对象 - 会自动转换为本地时区
    const date = new Date(dateTimeStr);
    
    // 格式化年月日
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // 格式化时分秒
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    
    // 组合成最终格式
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  } catch (error) {
    console.error('日期格式化错误:', error);
    return dateTimeStr;
  }
};

// 根据电量获取不同颜色
const getBatteryColor = (batteryLevel: number | string): string => {
  const level = Number(batteryLevel);
  if (level <= 20) {
    return '#f56c6c'; // 红色 - 低电量警告
  } else if (level <= 50) {
    return '#e6a23c'; // 橙色 - 中等电量
  } else {
    return '#67c23a'; // 绿色 - 高电量
  }
};

// 获取状态对应的样式类名
const getStatusClass = (status: string): string => {
  switch(status) {
    case '在线': return 'online';
    case '离线': return 'offline';
    case '故障': return 'error';
    case '充电中': return 'charging';
    default: return 'unknown';
  }
};
</script>

<style lang="less" scoped>
.status-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 增强选择器权重 */
.mod-device__vehiclerealtimestatus .status-icon {
  font-size: 18px;
}

/* 为每个状态单独定义颜色，增加选择器特异性 */
.mod-device__vehiclerealtimestatus {
  :deep(.el-icon.status-icon.online svg) {
    color: #67c23a !important; // 绿色
  }

  :deep(.el-icon.status-icon.offline svg) {
    color: #909399 !important; // 灰色
  }

  :deep(.el-icon.status-icon.error svg) {
    color: #f56c6c !important; // 红色
  }

  :deep(.el-icon.status-icon.charging svg) {
    color: #409eff !important; // 蓝色
  }
}

.status-text {
  font-size: 14px;
}

/* 新的状态显示样式 */
.status-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  
  &.online {
    background-color: #67c23a; // 绿色
    box-shadow: 0 0 5px rgba(103, 194, 58, 0.5);
  }
  
  &.offline {
    background-color: #909399; // 灰色
    box-shadow: 0 0 5px rgba(144, 147, 153, 0.5);
  }
  
  &.error {
    background-color: #f56c6c; // 红色
    box-shadow: 0 0 5px rgba(245, 108, 108, 0.5);
  }
  
  &.charging {
    background-color: #409eff; // 蓝色
    box-shadow: 0 0 5px rgba(64, 158, 255, 0.5);
  }
  
  &.unknown {
    background-color: #e6a23c; // 橙色
    box-shadow: 0 0 5px rgba(230, 162, 60, 0.5);
  }
}

/* 为不同状态设置文本颜色 */
.status-online .status-text {
  color: #67c23a;
  font-weight: 500;
}

.status-offline .status-text {
  color: #909399;
}

.status-error .status-text {
  color: #f56c6c;
  font-weight: 500;
}

.status-charging .status-text {
  color: #409eff;
  font-weight: 500;
}
</style>
